using Godot;
using System;
using System.Threading.Tasks;

/// <summary>
/// Barrel object that can be destroyed by pickaxe
/// Handles hit animations, health management, sprite animation frames, and resource dropping
/// </summary>
public partial class Barrel : Node2D, IDestroyableObject
{
	[Export] public int MaxHealth { get; set; } = 9;
	[Export] public AudioStream BarrelDestroyedAudio { get; set; }
	[Export] public AudioStream BarrelHit { get; set; }
	
	// Event for when barrel is destroyed
	[Signal] public delegate void BarrelDestroyedEventHandler(Vector2I tilePosition);

	private int _currentHealth;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;
	private EffectPlayer _effectPlayer;
	
	// Hit animation properties
	private readonly Color _hitColor = new Color(1.0f, 0.42f, 0.27f, 1.0f); // Orange tint for barrel
	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f; // 50% tint
	
	public override void _Ready()
	{
		_currentHealth = MaxHealth;
		_effectPlayer = GetNode<EffectPlayer>("EffectPlayer");
		// Add to barrels group for duplicate detection
		AddToGroup("barrels");

		// Get sprite component
		_sprite = GetNode<Sprite2D>("Sprite2D");
		if (_sprite == null)
		{
			GD.PrintErr("Barrel: Sprite2D node not found!");
			return;
		}

		// Get HP bar component
		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("Barrel: ProgressBar node not found!");
		}

		// Set up Y-sorting
		YSortEnabled = true;

		// Barrel is 48x16, so position sprite properly
		_sprite.Position = Vector2.Zero;

		// Find CustomDataLayerManager in scene
		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("Barrel: CustomDataLayerManager not found!");
		}

		// Only calculate tile position if it hasn't been set by spawner
		if (_tilePosition == Vector2I.Zero)
		{
			// Calculate tile position from world position (for manually placed barrels)
			_tilePosition = new Vector2I((int)(GlobalPosition.X - 8) / 16, (int)(GlobalPosition.Y - 8) / 16);

			// Mark tile as occupied (only for manually placed barrels)
			_customDataManager?.SetObjectPlaced(_tilePosition, ObjectTypePlaced.Rock);
		}

		// Initialize HP bar and sprite animation
		UpdateHPBar();
		UpdateSpriteFrame();

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	/// <summary>
	/// Take damage from pickaxe
	/// </summary>
	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;

		// Update HP bar and sprite frame
		UpdateHPBar();
		UpdateSpriteFrame();

		// Play hit animation
		PlayHitAnimation();

		// Check if barrel should be destroyed
		if (_currentHealth <= 0)
		{
			_effectPlayer.Play(BarrelDestroyedAudio);
			DestroyBarrel();
		}
		else
		{
			_effectPlayer.Play(BarrelHit);
		}
	}

	/// <summary>
	/// Update sprite frame based on current health
	/// 9-7hp = frame 0, 6-4hp = frame 1, 3-0hp = frame 2
	/// </summary>
	private void UpdateSpriteFrame()
	{
		if (_sprite == null) return;

		int frame = 0;
		if (_currentHealth >= 7)
		{
			frame = 0; // Full health
		}
		else if (_currentHealth >= 4)
		{
			frame = 1; // Medium damage
		}
		else
		{
			frame = 2; // Heavy damage
		}

		_sprite.Frame = frame;
	}

	/// <summary>
	/// Play hit animation (orange tint + scale effect)
	/// </summary>
	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		// Stop any existing tween
		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		// Orange tint effect
		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		// Scale effect (smaller then bigger)
		var originalScale = _sprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_sprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	/// <summary>
	/// Destroy the barrel and drop resources
	/// </summary>
	private async Task DestroyBarrel()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		// Drop resources
		DropResources();

		// Clear tile occupation
		_customDataManager?.ClearObjectPlaced(_tilePosition);

		// Give XP reward
		CommonSignals.Instance?.EmitAddXp(3);

		// Emit destruction signal
		EmitSignal(SignalName.BarrelDestroyed, _tilePosition);

		// Remove from scene
		await ToSignal(GetTree().CreateTimer(0.4f), "timeout");
		QueueFree();
	}

	/// <summary>
	/// Drop resources when barrel is destroyed (1 plank + 1 gold coin)
	/// </summary>
	private void DropResources()
	{
		// Spawn 1 plank
		Vector2 plankOffset = new Vector2(
			(float)(GD.Randf() - 0.5f) * 8.0f, // Random X offset: -4 to +4 pixels
			(float)(GD.Randf() - 0.5f) * 8.0f  // Random Y offset: -4 to +4 pixels
		);
		Vector2 plankPosition = GlobalPosition + plankOffset;
		DroppedResource.SpawnResource(plankPosition, ResourceType.Plank, 1);

		// Spawn 1 gold coin
		Vector2 coinOffset = new Vector2(
			(float)(GD.Randf() - 0.5f) * 8.0f, // Random X offset: -4 to +4 pixels
			(float)(GD.Randf() - 0.5f) * 8.0f  // Random Y offset: -4 to +4 pixels
		);
		Vector2 coinPosition = GlobalPosition + coinOffset;
		DroppedResource.SpawnCoin(coinPosition, 1);
	}

	/// <summary>
	/// Get current health
	/// </summary>
	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	/// <summary>
	/// Set current health
	/// </summary>
	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
		UpdateSpriteFrame();
	}

	/// <summary>
	/// Get tile position
	/// </summary>
	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	/// <summary>
	/// Set tile position (used by spawner)
	/// </summary>
	public void SetTilePosition(Vector2I position)
	{
		_tilePosition = position;
		// Position object in the center of the tile (tile size is 16x16)
		// Barrel is 1x1 tile but sprite is 48x16
		GlobalPosition = new Vector2(position.X * 16 + 8, position.Y * 16 + 8);
	}

	/// <summary>
	/// Check if this barrel can be hit by pickaxe from given position
	/// </summary>
	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		// Barrel can be hit if player is adjacent (including diagonally)
		var distance = _tilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	/// <summary>
	/// Update HP bar based on current health
	/// </summary>
	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		// Calculate health percentage
		float healthPercentage = (float)_currentHealth / MaxHealth;

		// If at full health, hide HP bar
		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			// Show HP bar and set progress
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (_tilePosition == tilePosition)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			if (CanBeHitFrom(playerTile))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}
}
