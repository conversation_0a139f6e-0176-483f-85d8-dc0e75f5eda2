TASK-1:
Add icon of a coin in texture manager. Add method GetCoinIconTexture() to TextureManager.

TASK-2:
In DroppedResource.cs add an option to set coin amount. If coin amount is set then use coin icon and display coin amount instead of ResourceType defined quantity. Also, when collected add coins to player inventory, not ResourceType.

TASK-3:
TutorialNPC spawns a chest that has 50 gold. When player opens chest - i want to spawn that 50 coins as collectable dropped items. Amount of coins should be added not when chest is opened but when player collects this resource (coin in this case).

TASK-4:
We have added to graveyard candle we added EmitShowKeyEPrompt and EmitHideKeyEPrompt when player is in range of graveyard candle. I want you to add the same logic to:
* shop (player detector is called PlayerCollider there)
* tutorialNpc when player is in it's range
* buildings: anvil, fireplace, furnaces (1-4), seed maker, grindstone, workbench