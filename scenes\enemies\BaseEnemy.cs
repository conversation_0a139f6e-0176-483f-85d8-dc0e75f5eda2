using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Base class for all enemies in the game
/// Provides core functionality for AI, combat, movement, and territory management
/// </summary>
public abstract partial class BaseEnemy : CharacterBody2D, IDestroyableObject, IEnemy
{
	// Core Properties
	[Export] public EnemyType EnemyType { get; set; } = EnemyType.Goblin;
	[Export] public int MaxHealth { get; set; } = 20;
	[Export] public int AttackDamage { get; set; } = 5;
	[Export] public float MovementSpeed { get; set; } = 30.0f;
	[Export] public float DetectionRange { get; set; } = 80.0f;
	[Export] public float AttackRange { get; set; } = 24.0f;
	[Export] public float TerritoryRadius { get; set; } = 120.0f;
	[Export] public int XpReward { get; set; } = 15;
	[Export] public float AttackCooldown { get; set; } = 2.0f;
	[Export] public float PatrolInterval { get; set; } = 3.0f;
	[Export] public float StunDuration { get; set; } = 0.5f;
	[Export] public float OptimalCombatDistance { get; set; } = 20.0f; // Distance to maintain from target

	// Territory & AI
	protected Vector2 _territoryCenter;
	protected EnemyState _currentState = EnemyState.Patrolling;
	protected Node2D _currentTarget;
	protected float _lastAttackTime = 0.0f;
	protected bool _isAggressive = false;
	protected EnemyBehaviorType _behaviorType = EnemyBehaviorType.Territorial;
	protected int _assignedRegion = 1;
	protected bool _wasHitByPlayer = false;
	protected Vector2 _lastKnownPlayerPosition;
	protected float _pursuitRange = 200.0f; // Range for losing target during pursuit
	protected float _aggressiveDetectionRange = float.MaxValue; // Unlimited range for aggressive enemies
	protected float _maxDistanceFromAssignedRegion = 300.0f; // Max distance from assigned region center

	// Movement & Pathfinding
	protected Vector2 _targetPosition;
	protected bool _isMoving = false;

	// Components
	protected int _currentHealth;
	protected Sprite2D _sprite;
	protected AnimationPlayer _animationPlayer;
	protected ProgressBar _hpBar;
	protected Timer _stateTimer;
	protected Timer _attackTimer;
	protected NavigationAgent2D _navigationAgent;
	protected CustomDataLayerManager _customDataManager;
	protected Area2D _detectionArea;
	protected Area2D _attackArea;
	protected CollisionShape2D _detectionShape;
	protected CollisionShape2D _attackShape;

	// State management
	protected bool _isBeingDestroyed = false;
	protected Vector2I _tilePosition;
	protected Random _random = new();
	protected string _lastDirection = "down";
	protected bool _isIdling = false;
	protected float _idleStartTime = 0.0f;
	protected float _idleDuration = 2.0f;

	// Flag to track if this enemy was loaded from save data
	private bool _wasLoadedFromSave = false;

	public override void _Ready()
	{
		// Disable gravity for 2D top-down movement
		MotionMode = MotionModeEnum.Floating;

		// Only initialize default values if not loaded from save
		if (!_wasLoadedFromSave)
		{
			// Initialize health from enemy type
			_currentHealth = EnemyType.GetMaxHealth();
			MaxHealth = _currentHealth;
			AttackDamage = EnemyType.GetAttackDamage();
			MovementSpeed = EnemyType.GetMovementSpeed();
			DetectionRange = EnemyType.GetDetectionRange();
			AttackRange = EnemyType.GetAttackRange();
			XpReward = EnemyType.GetXpReward();
		}

		SetupComponents();
		SetupNavigationAgent();
		SetupDetectionArea();
		SetupAttackArea();
		SetupTimers();

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		// Connect to combat signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		// Set initial territory center to spawn position
		if (_territoryCenter == Vector2.Zero)
		{
			_territoryCenter = GlobalPosition;
		}

		UpdateTilePosition();
		UpdateHPBar();
		ChangeState(EnemyState.Patrolling);

		// Only emit spawn signal for new enemies, not loaded ones
		if (!_wasLoadedFromSave)
		{
			CommonSignals.Instance?.EmitEnemySpawned(EnemyType, GlobalPosition, _assignedRegion);
		}
	}

	protected virtual void SetupComponents()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_hpBar = GetNode<ProgressBar>("ProgressBar");

		if (_hpBar != null)
		{
			_hpBar.Visible = false; // Hide initially
		}
	}

	protected virtual void SetupNavigationAgent()
	{
		// Try to get NavigationAgent2D from scene first, fallback to creating one
		_navigationAgent = GetNodeOrNull<NavigationAgent2D>("NavigationAgent2D");

		if (_navigationAgent == null)
		{
			// Fallback: create programmatically if not in scene
			_navigationAgent = new NavigationAgent2D();
			_navigationAgent.PathDesiredDistance = 8.0f;
			_navigationAgent.TargetDesiredDistance = 16.0f;
			_navigationAgent.PathMaxDistance = 3000.0f;
			_navigationAgent.AvoidanceEnabled = true;
			_navigationAgent.Radius = 16.0f;
			AddChild(_navigationAgent);
			GD.Print("Created NavigationAgent2D programmatically");
		}
		else
		{
			GD.Print("Using NavigationAgent2D from scene");
		}

		// Wait for navigation map to be ready
		_navigationAgent.NavigationFinished += OnNavigationFinished;
		CallDeferred(nameof(WaitForNavigationReady));
	}

	private void WaitForNavigationReady()
	{
		// Wait one frame for navigation system to initialize
		GetTree().ProcessFrame += () => {
			if (_navigationAgent != null && NavigationServer2D.MapGetCellSize(GetWorld2D().NavigationMap) > 0)
			{
				GD.Print($"Navigation ready for enemy at {GlobalPosition}");
			}
		};
	}

	private void OnNavigationFinished()
	{
		// Navigation path completed
		if (_currentState == EnemyState.Pursuing || _currentState == EnemyState.Returning)
		{
			// Continue with direct movement if navigation finished but target still exists
			_isMoving = true;
		}
	}

	protected virtual void SetupDetectionArea()
	{
		_detectionArea = new Area2D();
		_detectionArea.Name = "DetectionArea";
		_detectionShape = new CollisionShape2D();
		var detectionCircle = new CircleShape2D();
		detectionCircle.Radius = DetectionRange;
		_detectionShape.Shape = detectionCircle;
		_detectionArea.AddChild(_detectionShape);
		AddChild(_detectionArea);

		_detectionArea.BodyEntered += OnDetectionAreaEntered;
		_detectionArea.BodyExited += OnDetectionAreaExited;
		_detectionArea.AreaEntered += OnDetectionAreaEntered;
	}

	protected virtual void SetupAttackArea()
	{
		_attackArea = new Area2D();
		_attackArea.Name = "AttackArea";
		_attackShape = new CollisionShape2D();
		var attackCircle = new CircleShape2D();
		attackCircle.Radius = AttackRange;
		_attackShape.Shape = attackCircle;
		_attackArea.AddChild(_attackShape);
		AddChild(_attackArea);
	}

	protected virtual void SetupTimers()
	{
		_stateTimer = new Timer();
		_stateTimer.OneShot = true;
		_stateTimer.Timeout += OnStateTimerTimeout;
		AddChild(_stateTimer);

		_attackTimer = new Timer();
		_attackTimer.WaitTime = AttackCooldown;
		_attackTimer.OneShot = true;
		AddChild(_attackTimer);
	}

	public override void _PhysicsProcess(double delta)
	{
		if (_isBeingDestroyed) return;

		UpdateState(delta);
		HandleMovement(delta);
		CheckTargetDistance();
		UpdateTilePosition();
	}

	protected virtual void UpdateState(double delta)
	{
		switch (_currentState)
		{
			case EnemyState.Patrolling:
				HandlePatrolling(delta);
				break;
			case EnemyState.Pursuing:
				HandlePursuit(delta);
				break;
			case EnemyState.Attacking:
				HandleAttacking(delta);
				break;
			case EnemyState.Returning:
				HandleReturning(delta);
				break;
			case EnemyState.Stunned:
				HandleStunned(delta);
				break;
		}
	}

	protected virtual void HandlePatrolling(double delta)
	{
		if (!_isMoving && !_isIdling)
		{
			// Start idling
			_isIdling = true;
			_idleStartTime = Time.GetTicksMsec() / 1000.0f;

			// Aggressive enemies idle less (more active hunting)
			float idleMultiplier = _behaviorType == EnemyBehaviorType.Aggressive ? 0.5f : 1.0f;
			_idleDuration = (float)(_random.NextDouble() * 3.0f + 1.0f) * idleMultiplier;
			PlayAnimation("idle_" + _lastDirection);
		}
		else if (_isIdling)
		{
			// Check if idle period is over
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _idleStartTime >= _idleDuration)
			{
				_isIdling = false;
				ChoosePatrolTarget();
			}
		}

		// Always check for targets during patrolling
		CheckForTargets();

		// For aggressive enemies, check if we're too far from assigned region during patrolling
		// BUT ONLY during patrolling - not during pursuit or returning
		if (_behaviorType == EnemyBehaviorType.Aggressive && _currentState == EnemyState.Patrolling)
		{
			float distanceFromRegionCenter = GlobalPosition.DistanceTo(_territoryCenter);
			if (distanceFromRegionCenter > _maxDistanceFromAssignedRegion)
			{
				// Return to region center
				ChangeState(EnemyState.Returning);
				GD.Print($"Aggressive enemy too far from region during patrol ({distanceFromRegionCenter:F1} > {_maxDistanceFromAssignedRegion}), returning");
				return;
			}
		}
	}

	protected virtual void HandlePursuit(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			if (_behaviorType == EnemyBehaviorType.Territorial)
			{
				ChangeState(EnemyState.Returning);
			}
			else
			{
				ChangeState(EnemyState.Patrolling);
			}
			return;
		}

		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);

		// Check if target is in attack range
		if (distanceToTarget <= AttackRange)
		{
			ChangeState(EnemyState.Attacking);
			return;
		}

		// Check if we should lose target based on behavior type
		if (ShouldLoseTarget(distanceToTarget))
		{
			_currentTarget = null;
			if (_behaviorType == EnemyBehaviorType.Territorial)
			{
				ChangeState(EnemyState.Returning);
			}
			else
			{
				ChangeState(EnemyState.Patrolling);
			}
			return;
		}



		// Move towards target but stop at optimal combat distance
		if (distanceToTarget > OptimalCombatDistance)
		{
			Vector2 directionToTarget = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
			Vector2 optimalPosition = _currentTarget.GlobalPosition - directionToTarget * OptimalCombatDistance;
			MoveTowardsTarget(optimalPosition);


		}
		else
		{
			// We're at optimal distance, stop moving and face target
			_isMoving = false;
			Vector2 directionToTarget = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
			_lastDirection = GetDirectionString(directionToTarget);
			PlayAnimation("idle_" + _lastDirection);
		}
	}

	protected virtual void HandleAttacking(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(EnemyState.Patrolling);
			return;
		}

		// Check if target is still in range
		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		if (distanceToTarget > AttackRange)
		{
			ChangeState(EnemyState.Pursuing);
			return;
		}

		// Attack if cooldown is ready
		if (_attackTimer.IsStopped())
		{
			ExecuteAttack();
			_attackTimer.Start();
		}
	}

	protected virtual void HandleReturning(double delta)
	{
		// Move back to territory/region center
		MoveTowardsTarget(_territoryCenter);

		// Check if we're back in acceptable range
		float acceptableDistance = _behaviorType == EnemyBehaviorType.Territorial ?
			TerritoryRadius * 0.8f : _maxDistanceFromAssignedRegion * 0.7f;

		if (GlobalPosition.DistanceTo(_territoryCenter) <= acceptableDistance)
		{
			ChangeState(EnemyState.Patrolling);
			return;
		}

		// While returning, still check for threats
		if (_behaviorType == EnemyBehaviorType.Aggressive || _wasHitByPlayer)
		{
			// Continue scanning for targets while returning
			CheckForAggressiveTargets();
		}
		else
		{
			// Territorial enemies: Check if player enters territory while we're returning
			var player = GetNode<PlayerController>("/root/world/Player");
			if (player != null)
			{
				float distanceFromTerritory = player.GlobalPosition.DistanceTo(_territoryCenter);
				if (distanceFromTerritory <= TerritoryRadius)
				{
					float distanceToPlayer = GlobalPosition.DistanceTo(player.GlobalPosition);
					if (distanceToPlayer <= DetectionRange)
					{
						_currentTarget = player;
						ChangeState(EnemyState.Pursuing);
					}
				}
			}
		}
	}

	protected virtual void HandleStunned(double delta)
	{
		// Do nothing while stunned - timer will handle state change
	}

	protected virtual void HandleMovement(double delta)
	{
		if (_currentState == EnemyState.Stunned)
		{
			Velocity = Vector2.Zero;
			MoveAndSlide();
			return;
		}

		if (!_isMoving)
		{
			Velocity = Vector2.Zero;
			MoveAndSlide();
			return;
		}

		Vector2 direction;
		float distanceToTarget;

		// Use NavigationAgent2D if available and target not yet reached
		if (_navigationAgent != null && !_navigationAgent.IsTargetReached() && _navigationAgent.IsNavigationFinished() == false)
		{
			Vector2 nextPosition = _navigationAgent.GetNextPathPosition();
			direction = (nextPosition - GlobalPosition).Normalized();
			distanceToTarget = GlobalPosition.DistanceTo(nextPosition);
		}
		else
		{
			// Fallback to direct movement when navigation finished or target reached
			direction = (_targetPosition - GlobalPosition).Normalized();
			distanceToTarget = GlobalPosition.DistanceTo(_targetPosition);
		}

		// Stop if we're close enough to target
		if (distanceToTarget <= 5.0f)
		{
			_isMoving = false;
			Velocity = Vector2.Zero;
			MoveAndSlide();
			return;
		}

		// Set velocity towards target
		Velocity = direction * MovementSpeed;

		// Move using physics - only call MoveAndSlide once per frame
		MoveAndSlide();

		// Update animation based on actual movement
		if (Velocity.Length() > 0)
		{
			UpdateFacingDirection(Velocity.Normalized());
		}
	}

	protected virtual void ChoosePatrolTarget()
	{
		Vector2 patrolTarget;

		if (_behaviorType == EnemyBehaviorType.Aggressive)
		{
			// Aggressive enemies roam more freely but stay within assigned region bounds
			float angle = (float)(_random.NextDouble() * Math.PI * 2);
			float distance = (float)(_random.NextDouble() * 150.0f + 50.0f); // 50-200 units from current position

			patrolTarget = GlobalPosition + new Vector2(
				Mathf.Cos(angle) * distance,
				Mathf.Sin(angle) * distance
			);

			// Enforce region boundary - if too far from territory center, move back toward it
			float distanceFromTerritory = patrolTarget.DistanceTo(_territoryCenter);
			if (distanceFromTerritory > _maxDistanceFromAssignedRegion)
			{
				// Choose a target closer to territory center
				Vector2 directionToTerritory = (_territoryCenter - GlobalPosition).Normalized();
				float safeDistance = (float)(_random.NextDouble() * 100.0f + 50.0f);
				patrolTarget = GlobalPosition + directionToTerritory * safeDistance;
			}

		}
		else
		{
			// Territorial enemies patrol within their territory but can leave during pursuit
			float angle = (float)(_random.NextDouble() * Math.PI * 2);
			float distance = (float)(_random.NextDouble() * TerritoryRadius * 0.8f);

			patrolTarget = _territoryCenter + new Vector2(
				Mathf.Cos(angle) * distance,
				Mathf.Sin(angle) * distance
			);
		}

		MoveTowardsTarget(patrolTarget);
	}

	protected virtual void MoveTowardsTarget(Vector2 targetPos)
	{
		_targetPosition = targetPos;
		_isMoving = true;

		// Use NavigationAgent2D for pathfinding if available
		if (_navigationAgent != null)
		{
			// Only set target if navigation is ready and target is far enough to need pathfinding
			float directDistance = GlobalPosition.DistanceTo(targetPos);
			if (directDistance > 32.0f) // Use navigation for longer distances
			{
				_navigationAgent.TargetPosition = targetPos;
			}
		}

		// Update facing direction immediately
		Vector2 direction = (targetPos - GlobalPosition).Normalized();
		_lastDirection = GetDirectionString(direction);
	}

	protected virtual void UpdateFacingDirection(Vector2 direction)
	{
		// Update last direction for consistent facing
		_lastDirection = GetDirectionString(direction);

		// Play appropriate movement animation based on direction and movement state
		if (_isMoving && Velocity.Length() > 5.0f)
		{
			string animationName = "move_" + _lastDirection;
			PlayAnimation(animationName);
		}
		else
		{
			string animationName = "idle_" + _lastDirection;
			PlayAnimation(animationName);
		}
	}

	protected virtual string GetDirectionString(Vector2 direction)
	{
		if (Math.Abs(direction.X) > Math.Abs(direction.Y))
		{
			return direction.X > 0 ? "right" : "left";
		}
		else
		{
			return direction.Y > 0 ? "down" : "up";
		}
	}

	protected virtual Vector2 GetAlternativeDirection(Vector2 originalDirection)
	{
		// Try perpendicular directions when stuck
		Vector2[] alternatives = {
			new Vector2(-originalDirection.Y, originalDirection.X), // 90 degrees left
			new Vector2(originalDirection.Y, -originalDirection.X), // 90 degrees right
			new Vector2(originalDirection.X * 0.7f - originalDirection.Y * 0.7f, originalDirection.Y * 0.7f + originalDirection.X * 0.7f), // 45 degrees
			new Vector2(originalDirection.X * 0.7f + originalDirection.Y * 0.7f, originalDirection.Y * 0.7f - originalDirection.X * 0.7f)  // -45 degrees
		};

		// Return a random alternative direction
		return alternatives[_random.Next(alternatives.Length)].Normalized();
	}

	protected virtual void PlayAnimation(string animationName)
	{
		if (_animationPlayer != null && _animationPlayer.HasAnimation(animationName))
		{
			_animationPlayer.Play(animationName);
		}
	}

	protected virtual void CheckForTargets()
	{
		// Behavior-specific target detection
		if (_behaviorType == EnemyBehaviorType.Territorial)
		{
			CheckForTerritorialTargets();
		}
		else
		{
			CheckForAggressiveTargets();
		}
	}

	protected virtual void CheckForTerritorialTargets()
	{
		// Territorial enemies only attack if player enters territory or they were hit
		if (!_wasHitByPlayer)
		{
			var player = GetNode<PlayerController>("/root/world/Player");
			if (player != null)
			{
				float distanceFromTerritory = player.GlobalPosition.DistanceTo(_territoryCenter);
				if (distanceFromTerritory <= TerritoryRadius)
				{
					float distanceToPlayer = GlobalPosition.DistanceTo(player.GlobalPosition);
					if (distanceToPlayer <= DetectionRange)
					{
						_currentTarget = player;
						ChangeState(EnemyState.Pursuing);
						GD.Print($"Territorial enemy at {GlobalPosition} started pursuing player (distance: {distanceToPlayer})");
						return;
					}
				}
			}
		}
		else
		{
			// If hit by player, use enhanced target scanning
			CheckForAggressiveTargets();
		}
	}

	protected virtual void CheckForAggressiveTargets()
	{
		// Aggressive enemies scan for all available targets
		var potentialTargets = new List<Node2D>();

		// Use appropriate detection range based on behavior type
		float effectiveDetectionRange = _behaviorType == EnemyBehaviorType.Aggressive ?
			_aggressiveDetectionRange : DetectionRange;

		// Check for player
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			float distanceToPlayer = GlobalPosition.DistanceTo(player.GlobalPosition);
			if (distanceToPlayer <= effectiveDetectionRange)
			{
				potentialTargets.Add(player);
			}
		}

		// Check for buildings that implement ICombatTarget
		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			foreach (Node child in buildingsNode.GetChildren())
			{
				if (child is ICombatTarget combatTarget && child is Node2D buildingNode)
				{
					float distance = GlobalPosition.DistanceTo(buildingNode.GlobalPosition);
					if (distance <= effectiveDetectionRange && combatTarget.CanBeTargeted())
					{
						potentialTargets.Add(buildingNode);
					}
				}
			}
		}

		if (potentialTargets.Count > 0)
		{
			var bestTarget = SelectBestTarget(potentialTargets);
			if (bestTarget != null)
			{
				_currentTarget = bestTarget;
				ChangeState(EnemyState.Pursuing);
				GD.Print($"Aggressive enemy at {GlobalPosition} started pursuing {bestTarget.GetType().Name} at distance {GlobalPosition.DistanceTo(bestTarget.GlobalPosition):F1}");
			}
		}
	}

	protected virtual Node2D SelectBestTarget(List<Node2D> targets)
	{
		if (targets.Count == 0) return null;

		// Enhanced target selection with behavior-specific logic
		var targetScores = new List<(Node2D target, float score)>();

		foreach (var target in targets)
		{
			float score = CalculateTargetScore(target);
			targetScores.Add((target, score));
		}

		// Sort by score (highest first) and return best target
		var bestTarget = targetScores.OrderByDescending(t => t.score).FirstOrDefault();
		return bestTarget.target;
	}

	protected virtual float CalculateTargetScore(Node2D target)
	{
		if (target == null) return 0;

		// Base priority score
		float priorityScore = 0;
		if (target is PlayerController)
		{
			priorityScore = (int)TargetType.Player;
		}
		else if (target is ICombatTarget combatTarget)
		{
			priorityScore = (int)combatTarget.GetTargetType();
		}

		// Distance factor (closer targets get higher scores)
		float distance = GlobalPosition.DistanceTo(target.GlobalPosition);
		float distanceScore = Mathf.Max(0, 100 - distance); // Closer = higher score

		// Behavior-specific modifiers
		float behaviorModifier = 1.0f;
		if (_behaviorType == EnemyBehaviorType.Aggressive)
		{
			// Aggressive enemies prefer player targets more strongly
			if (target is PlayerController)
			{
				behaviorModifier = 1.5f;
			}
		}
		else
		{
			// Territorial enemies prefer closer targets
			if (distance <= TerritoryRadius)
			{
				behaviorModifier = 1.3f;
			}
		}

		// Current target bonus (prefer to stick with current target unless much better option)
		float currentTargetBonus = 0;
		if (target == _currentTarget)
		{
			currentTargetBonus = 20; // Small bonus to prevent constant switching
		}

		return (priorityScore * 100 + distanceScore) * behaviorModifier + currentTargetBonus;
	}

	protected virtual bool ShouldReturnToTerritory()
	{
		float distanceFromTerritory = GlobalPosition.DistanceTo(_territoryCenter);
		return distanceFromTerritory > TerritoryRadius;
	}

	protected virtual bool ShouldLoseTarget(float distanceToTarget)
	{
		// Territorial enemies lose target when beyond pursuit range
		if (_behaviorType == EnemyBehaviorType.Territorial)
		{
			return distanceToTarget > _pursuitRange;
		}

		// Aggressive enemies have unlimited pursuit range - they never lose target due to distance
		// They only lose target if the target is destroyed or becomes invalid
		return false;
	}

	protected virtual void CheckTargetDistance()
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget)) return;

		float distance = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);

		// Lose target if too far away
		if (distance > DetectionRange * 1.5f)
		{
			_currentTarget = null;
			if (_currentState == EnemyState.Pursuing || _currentState == EnemyState.Attacking)
			{
				if (_behaviorType == EnemyBehaviorType.Territorial)
				{
					ChangeState(EnemyState.Returning);
				}
				else
				{
					ChangeState(EnemyState.Patrolling);
				}
			}
		}

		// Dynamic target switching - check for better targets periodically
		if (_behaviorType == EnemyBehaviorType.Aggressive && _currentState == EnemyState.Pursuing)
		{
			CheckForBetterTarget();
		}
	}

	protected virtual void CheckForBetterTarget()
	{
		// Only check every few frames to avoid performance issues
		if (Engine.GetProcessFrames() % 30 != 0) return;

		var potentialTargets = new List<Node2D>();

		// Check for player
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null && GlobalPosition.DistanceTo(player.GlobalPosition) <= DetectionRange)
		{
			potentialTargets.Add(player);
		}

		// Check for buildings
		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			foreach (Node child in buildingsNode.GetChildren())
			{
				if (child is ICombatTarget combatTarget && child is Node2D buildingNode)
				{
					float distance = GlobalPosition.DistanceTo(buildingNode.GlobalPosition);
					if (distance <= DetectionRange && combatTarget.CanBeTargeted())
					{
						potentialTargets.Add(buildingNode);
					}
				}
			}
		}

		if (potentialTargets.Count > 0)
		{
			var bestTarget = SelectBestTarget(potentialTargets);
			if (bestTarget != null && bestTarget != _currentTarget)
			{
				// Only switch if the new target is significantly better
				float currentScore = CalculateTargetScore(_currentTarget);
				float newScore = CalculateTargetScore(bestTarget);

				if (newScore > currentScore + 50) // Threshold to prevent constant switching
				{
					_currentTarget = bestTarget;
				}
			}
		}
	}

	protected abstract void ExecuteAttack();

	protected virtual void ChangeState(EnemyState newState)
	{
		if (_currentState == newState) return;

		_currentState = newState;
		OnStateChanged(newState);
	}

	protected virtual void OnStateChanged(EnemyState newState)
	{
		switch (newState)
		{
			case EnemyState.Patrolling:
				// Only start patrol timer if not coming from stunned state with idle flag
				if (!_isIdling)
				{
					_stateTimer.WaitTime = PatrolInterval;
					_stateTimer.Start();
				}
				PlayAnimation("idle_" + _lastDirection);
				break;
			case EnemyState.Pursuing:
				// Animation handled in movement
				break;
			case EnemyState.Attacking:
				// Animation handled in attack
				break;
			case EnemyState.Returning:
				// Animation handled in movement
				break;
			case EnemyState.Stunned:
				// Stop any movement and play hit animation in current direction
				_isMoving = false;
				PlayAnimation("got_hit_" + _lastDirection);
				_stateTimer.WaitTime = StunDuration;
				_stateTimer.Start();
				break;
		}
	}

	protected virtual void OnStateTimerTimeout()
	{
		switch (_currentState)
		{
			case EnemyState.Patrolling:
				if (!_isMoving)
				{
					ChoosePatrolTarget();
				}
				_stateTimer.WaitTime = PatrolInterval;
				_stateTimer.Start();
				break;
			case EnemyState.Stunned:
				// After stun, check if we should pursue target or return to patrolling
				if (_wasHitByPlayer && _currentTarget != null && IsInstanceValid(_currentTarget))
				{
					// If we have a target and were hit, pursue the target
					ChangeState(EnemyState.Pursuing);
				}
				else
				{
					// Otherwise, go to idle state and face the preserved direction
					_isMoving = false;
					_isIdling = true;
					_idleStartTime = Time.GetTicksMsec() / 1000.0f;
					_idleDuration = 1.0f; // Short idle after stun
					ChangeState(EnemyState.Patrolling);
				}
				break;
		}
	}

	// IDestroyableObject implementation
	public virtual void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		UpdateHPBar();
		ShowHPBar();

		_wasHitByPlayer = true;

		// Store current direction before changing to stunned state
		string directionBeforeHit = _lastDirection;

		ChangeState(EnemyState.Stunned);

		// Ensure direction is preserved after hit animation
		_lastDirection = directionBeforeHit;

		// CRITICAL FIX: Enforce minimum 1s attack delay when hit
		if (_attackTimer != null)
		{
			double currentWaitTime = _attackTimer.WaitTime;
			if (currentWaitTime < 1.0)
			{
				_attackTimer.WaitTime = 1.0;
			}
			// Restart the timer to apply the delay
			_attackTimer.Start();
		}

		CommonSignals.Instance?.EmitEnemyTookDamage(EnemyType, GlobalPosition, damage);

		if (_currentHealth <= 0)
		{
			Die();
		}
	}

	public virtual bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		Vector2I enemyTile = new Vector2I(
			Mathf.FloorToInt(GlobalPosition.X / 16),
			Mathf.FloorToInt(GlobalPosition.Y / 16)
		);
		
		float distance = playerTilePosition.DistanceTo(enemyTile);
		return distance <= 2.0f;
	}

	public virtual Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	public virtual int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public virtual void SetCurrentHealth(int health)
	{
		_currentHealth = health;
		UpdateHPBar();
	}

	// IEnemy implementation
	public EnemyState GetCurrentState()
	{
		return _currentState;
	}

	public void SetTarget(Node2D target)
	{
		_currentTarget = target;
		if (target != null)
		{
			ChangeState(EnemyState.Pursuing);
		}
	}

	public void SetTerritory(Vector2 center, float radius)
	{
		_territoryCenter = center;
		TerritoryRadius = radius;
	}

	public virtual void SetAssignedRegion(int regionId, Vector2 regionCenter)
	{
		_assignedRegion = regionId;
		_territoryCenter = regionCenter;
		GD.Print($"Enemy assigned to region {regionId} with center at {regionCenter}");
	}

	public void SetAggressive(bool aggressive)
	{
		_isAggressive = aggressive;
	}

	public bool IsAggressive()
	{
		return _isAggressive;
	}

	public void SetBehaviorType(EnemyBehaviorType behaviorType)
	{
		_behaviorType = behaviorType;
		// Update aggressive flag for backward compatibility
		_isAggressive = (behaviorType == EnemyBehaviorType.Aggressive);

		// Adjust detection range based on behavior
		if (_behaviorType == EnemyBehaviorType.Aggressive)
		{
			DetectionRange = _aggressiveDetectionRange;
			GD.Print($"Enemy at {GlobalPosition} set to AGGRESSIVE behavior (detection range: {DetectionRange})");
		}
		else
		{
			DetectionRange = 80.0f; // Default territorial range
			GD.Print($"Enemy at {GlobalPosition} set to TERRITORIAL behavior (detection range: {DetectionRange})");
		}
	}

	public EnemyBehaviorType GetBehaviorType()
	{
		return _behaviorType;
	}

	public EnemyType GetEnemyType()
	{
		return EnemyType;
	}

	public int GetRegion()
	{
		return _assignedRegion;
	}

	public void SetRegion(int regionId)
	{
		_assignedRegion = regionId;
	}

	protected virtual void UpdateTilePosition()
	{
		_tilePosition = new Vector2I(
			Mathf.FloorToInt(GlobalPosition.X / 16),
			Mathf.FloorToInt(GlobalPosition.Y / 16)
		);
	}

	protected virtual void UpdateHPBar()
	{
		if (_hpBar != null)
		{
			float healthPercentage = (float)_currentHealth / MaxHealth;
			_hpBar.SetProgress(healthPercentage);

			// Keep health bar visible if health is below max
			if (_currentHealth < MaxHealth)
			{
				_hpBar.Visible = true;
			}

			GD.Print($"Enemy HP Bar: {_currentHealth}/{MaxHealth} = {healthPercentage:F2} ({healthPercentage * 100:F1}%)");
		}
		else
		{
			GD.PrintErr("Enemy HP Bar is null!");
		}
	}

	protected virtual void ShowHPBar()
	{
		if (_hpBar != null)
		{
			_hpBar.Visible = true;

			// Only hide HP bar after 3 seconds if health is at maximum
			if (_currentHealth >= MaxHealth)
			{
				GetTree().CreateTimer(3.0f).Timeout += () => {
					if (_hpBar != null && IsInstanceValid(this) && _currentHealth >= MaxHealth)
					{
						_hpBar.Visible = false;
					}
				};
			}
			// If health is below max, keep it visible permanently
		}
	}

	protected virtual void Die()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		DropResources();
		CommonSignals.Instance?.EmitAddXp(XpReward);
		CommonSignals.Instance?.EmitEnemyDefeated(EnemyType, GlobalPosition, XpReward);

		// Clear tile occupation if set
		if (_customDataManager != null)
		{
			_customDataManager.ClearObjectPlaced(_tilePosition);
		}

		QueueFree();
	}

	protected virtual void DropResources()
	{
		// Base enemies don't drop resources by default
		// Override in specific enemy types to add drops
	}

	protected virtual void OnDetectionAreaEntered(Node2D body)
	{
		if (body is PlayerController && (_isAggressive || _wasHitByPlayer))
		{
			if (_currentTarget == null)
			{
				SetTarget(body);
			}
		}
	}

	protected virtual void OnDetectionAreaExited(Node2D body)
	{
		// Target will be lost through distance checking
	}

	protected virtual void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		// Check if this enemy is in the sword's attack arc
		if (IsInSwordAttackArc(playerPosition, attackDirection))
		{
			// No knockback - just take damage
			TakeDamage(2); // Sword damage
		}
	}

	protected virtual bool IsInSwordAttackArc(Vector2 playerPosition, Vector2 attackDirection)
	{
		float distance = GlobalPosition.DistanceTo(playerPosition);
		if (distance > 32.0f) return false; // Sword range

		Vector2 directionToEnemy = (GlobalPosition - playerPosition).Normalized();
		float dotProduct = attackDirection.Dot(directionToEnemy);
		
		return dotProduct > 0.5f; // ~60 degree arc
	}



	public virtual EnemySaveData GetSaveData()
	{
		return new EnemySaveData
		{
			Position = GlobalPosition,
			Health = _currentHealth,
			AssignedRegion = _assignedRegion,
			EnemyType = EnemyType,
			CurrentState = _currentState,
			TerritoryCenter = _territoryCenter,
			TerritoryRadius = TerritoryRadius,
			IsAggressive = _isAggressive,
			BehaviorType = _behaviorType,
			LastAttackTime = _lastAttackTime,
			WasHitByPlayer = _wasHitByPlayer,
			LastKnownPlayerPosition = _lastKnownPlayerPosition,
			TargetPosition = _targetPosition
		};
	}

	public virtual void LoadFromSaveData(EnemySaveData data)
	{
		// Set flag to prevent _Ready from overriding loaded data
		_wasLoadedFromSave = true;

		GlobalPosition = data.Position;
		_assignedRegion = data.AssignedRegion;
		EnemyType = data.EnemyType;
		_territoryCenter = data.TerritoryCenter;
		TerritoryRadius = data.TerritoryRadius;
		_isAggressive = data.IsAggressive;
		_behaviorType = data.BehaviorType;
		_lastAttackTime = data.LastAttackTime;
		_wasHitByPlayer = data.WasHitByPlayer;
		_lastKnownPlayerPosition = data.LastKnownPlayerPosition;
		_targetPosition = data.TargetPosition;

		// Set stats from enemy type (since _Ready won't do it)
		MaxHealth = EnemyType.GetMaxHealth();
		AttackDamage = EnemyType.GetAttackDamage();
		MovementSpeed = EnemyType.GetMovementSpeed();
		DetectionRange = EnemyType.GetDetectionRange();
		AttackRange = EnemyType.GetAttackRange();
		XpReward = EnemyType.GetXpReward();

		// Adjust detection range based on behavior type
		if (_behaviorType == EnemyBehaviorType.Aggressive)
		{
			DetectionRange = _aggressiveDetectionRange;
		}

		// Set health after MaxHealth is set, and validate it
		_currentHealth = data.Health;
		if (_currentHealth <= 0 || _currentHealth > MaxHealth)
		{
			_currentHealth = MaxHealth; // Reset to full health if invalid
			GD.Print($"Enemy: Fixed invalid health ({data.Health}), set to {MaxHealth}");
		}

		UpdateTilePosition();
		CallDeferred(nameof(InitializeStateAfterReady), (int)data.CurrentState);
	}

	protected virtual void InitializeStateAfterReady(int stateInt)
	{
		if (_animationPlayer != null)
		{
			EnemyState state = (EnemyState)stateInt;
			ChangeState(state);
		}
	}



	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}
		base._ExitTree();
	}
}
