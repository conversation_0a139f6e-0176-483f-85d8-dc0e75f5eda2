TASK-1: Fix error:
  ERROR: ResourcesManager: Failed to deserialize enemy data for region 5: The JSON value could not be converted to EnemyBehaviorType. Path: $[0].behaviorType | LineNumber: 0 | BytePositionInLine: 220.

TASK-2:
Create a scene (node2d) call it <PERSON><PERSON>. Add script to it that will call every 5s (to be defined in inspector):

if(!GetNode<NavigationRegion2D>("/root/world/NavigationRegion2D").IsBaking())
GetNode<NavigationRegion2D>("/root/world/NavigationRegion2D").BakeNavigationPolygon(true);

so that it bakes navigation region. Also, it would be best to have a static method in Bakery that will allow me to call it like Bakery.Bake(); and it should bake navigation region.