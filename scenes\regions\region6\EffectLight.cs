using Godot;

public partial class EffectLight : Node2D
{
	[Export] public float LightRadius { get; set; } = 64.0f;
	[Export] public Color LightColor { get; set; } = new Color(0.8f, 0.9f, 1.0f, 1.0f); // Cool magical light
	[Export] public float LightEnergy { get; set; } = 1.0f;
	[Export] public bool OnlyShowAtNight { get; set; } = true;
	[Export] public float FlickerIntensity { get; set; } = 0.2f;
	[Export] public float FlickerSpeed { get; set; } = 2.5f;

	private Light2D _light;
	private DayNightManager _dayNightManager;
	private float _baseEnergy;
	private float _flickerTime = 0.0f;

	public override void _Ready()
	{
		// Get the Light2D node from the scene
		_light = GetParent().GetNode<Light2D>("EffectLight");
		if (_light == null)
		{
			GD.PrintErr("EffectLight: Light2D node not found in scene!");
			return;
		}

		_light.Enabled = true;
		_light.Energy = LightEnergy;
		_light.Color = LightColor;
		_light.Scale = Vector2.One * (LightRadius / 64.0f); // Scale the light

		_baseEnergy = LightEnergy;

		// Find the DayNightManager
		_dayNightManager = GetNode<DayNightManager>("/root/world/DayNight");
		if (_dayNightManager == null)
		{
			// Try alternative path
			_dayNightManager = GetTree().GetFirstNodeInGroup("DayNightManager") as DayNightManager;
			if (_dayNightManager == null)
			{
				GD.PrintErr("EffectLight: DayNightManager not found!");
			}
		}

		GD.Print($"EffectLight: Initialized with radius {LightRadius}");
	}

	public override void _Process(double delta)
	{
		if (_light == null || _dayNightManager == null) return;

		// Show/hide light based on day/night cycle and parent visibility
		var parent = GetParent() as Node2D;
		bool parentVisible = parent?.Visible ?? true;

		if (OnlyShowAtNight && parentVisible)
		{
			_light.Visible = _dayNightManager.IsNight();
		}
		else if (parentVisible)
		{
			_light.Visible = true;
		}
		else
		{
			_light.Visible = false;
		}

		// Add flickering effect for magical light
		if (_light.Visible)
		{
			_flickerTime += (float)delta * FlickerSpeed;
			float flicker = Mathf.Sin(_flickerTime) * FlickerIntensity;
			_light.Energy = _baseEnergy + flicker;
		}
	}

	public void SetLightRadius(float radius)
	{
		LightRadius = radius;
		if (_light != null)
		{
			_light.Scale = Vector2.One * (radius / 64.0f);
		}
		GD.Print($"EffectLight: Radius set to {radius}");
	}

	public void SetLightColor(Color color)
	{
		LightColor = color;
		if (_light != null)
		{
			_light.Color = color;
		}
	}

	public void SetLightEnergy(float energy)
	{
		LightEnergy = energy;
		_baseEnergy = energy;
		if (_light != null)
		{
			_light.Energy = energy;
		}
	}
}
