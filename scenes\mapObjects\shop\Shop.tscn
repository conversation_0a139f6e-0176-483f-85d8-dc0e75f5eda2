[gd_scene load_steps=11 format=3 uid="uid://j6jd2tdkx7qu"]

[ext_resource type="Texture2D" uid="uid://khix3rrncf7a" path="res://resources/solaria/buildings/shop/shopBack.png" id="1_6m6m1"]
[ext_resource type="Script" uid="uid://bi2i32cv48bus" path="res://scenes/mapObjects/shop/Shop.cs" id="1_shop_script"]
[ext_resource type="Texture2D" uid="uid://drculs1amfvkw" path="res://resources/solaria/buildings/shop/shopFront.png" id="2_4jmhs"]
[ext_resource type="Texture2D" uid="uid://c4pr73cegkkl0" path="res://resources/solaria/SpritePack/Base/Character/Idle.png" id="3_wt7j0"]
[ext_resource type="PackedScene" uid="uid://d188nhrfdrene" path="res://scenes/mapObjects/shop/ShopMenu.tscn" id="5_wt7j0"]

[sub_resource type="Animation" id="Animation_wt7j0"]
resource_name = "Animate"
length = 0.6
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [6, 7, 8, 9, 10, 11]
}

[sub_resource type="Animation" id="Animation_o07kr"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [6]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_rt7f8"]
_data = {
&"Animate": SubResource("Animation_wt7j0"),
&"RESET": SubResource("Animation_o07kr")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_6m6m1"]
size = Vector2(126, 62)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_4jmhs"]
size = Vector2(131, 79)

[node name="Shop" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_shop_script")

[node name="Background" type="Sprite2D" parent="."]
z_index = -1
texture = ExtResource("1_6m6m1")

[node name="Foreground" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("2_4jmhs")

[node name="NPC2" type="Sprite2D" parent="."]
z_index = -1
y_sort_enabled = true
position = Vector2(-40, 27)
texture = ExtResource("3_wt7j0")
hframes = 6
vframes = 4
frame = 6

[node name="NPCAnimationPlayer" type="AnimationPlayer" parent="NPC2"]
libraries = {
&"": SubResource("AnimationLibrary_rt7f8")
}
speed_scale = 0.5

[node name="NPC1" type="Sprite2D" parent="."]
z_index = -1
position = Vector2(42, 26)
texture = ExtResource("3_wt7j0")
hframes = 6
vframes = 4
frame = 6

[node name="NPCAnimationPlayer" type="AnimationPlayer" parent="NPC1"]
libraries = {
&"": SubResource("AnimationLibrary_rt7f8")
}
speed_scale = 0.5

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 15)
shape = SubResource("RectangleShape2D_6m6m1")

[node name="PlayerCollider" type="Area2D" parent="." groups=["navigation_polygon_source_geometry_group"]]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerCollider"]
position = Vector2(-0.5, 6.5)
shape = SubResource("RectangleShape2D_4jmhs")

[node name="ShopMenu" parent="." instance=ExtResource("5_wt7j0")]
