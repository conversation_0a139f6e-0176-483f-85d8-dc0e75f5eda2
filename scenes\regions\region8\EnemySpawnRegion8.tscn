[gd_scene load_steps=16 format=3 uid="uid://dq3424ky4siwr"]

[ext_resource type="Texture2D" uid="uid://l20mmss22r5y" path="res://resources/ELV_itchio/GreenPlains/region_8_enemy_spawn_bg.png" id="1_4yfa4"]
[ext_resource type="Script" uid="uid://dy7wnflliega6" path="res://scenes/regions/region8/EnemySpawner.cs" id="1_spawner"]
[ext_resource type="Texture2D" uid="uid://bxv46bqo1b0us" path="res://resources/ELV_itchio/GreenPlains/region_8_enemy_fireplace_2.png" id="2_yjm1x"]
[ext_resource type="Texture2D" uid="uid://dt2nlosyiisqi" path="res://resources/ELV_itchio/GreenPlains/region_8_enemy_spawn_front_1.png" id="3_03agl"]
[ext_resource type="Texture2D" uid="uid://b7evnysbwhigh" path="res://resources/ELV_itchio/GreenPlains/region_8_enemy_spawn_front_1_2.png" id="4_03agl"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="4_iy0sv"]
[ext_resource type="Texture2D" uid="uid://dtg3v4ao3agbk" path="res://resources/ELV_itchio/GreenPlains/region_8_enemy_spawn_front_2.png" id="5_iy0sv"]

[sub_resource type="Animation" id="Animation_cttc0"]
resource_name = "Animate"
length = 0.8
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [1, 2, 3, 4, 5, 6, 7, 8]
}

[sub_resource type="Animation" id="Animation_ih68c"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_03agl"]
_data = {
&"Animate": SubResource("Animation_cttc0"),
&"RESET": SubResource("Animation_ih68c")
}

[sub_resource type="Animation" id="Animation_iorkd"]
resource_name = "Animate"
length = 0.8
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7]
}

[sub_resource type="Animation" id="Animation_nrsbb"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_luem1"]
_data = {
&"Animate": SubResource("Animation_iorkd"),
&"RESET": SubResource("Animation_nrsbb")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_ih68c"]
size = Vector2(81, 21)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_03agl"]
size = Vector2(16, 16)

[node name="EnemySpawnRegion8" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_spawner")
SpawnInterval = 2.0

[node name="Background" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -66)
texture = ExtResource("1_4yfa4")
offset = Vector2(0, 66)

[node name="Fireplace1" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(-48, 28)
texture = ExtResource("2_yjm1x")
hframes = 9

[node name="AnimationPlayer" type="AnimationPlayer" parent="Fireplace1"]
libraries = {
&"": SubResource("AnimationLibrary_03agl")
}

[node name="Fireplace2" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(49, 28)
texture = ExtResource("2_yjm1x")
hframes = 9

[node name="AnimationPlayer" type="AnimationPlayer" parent="Fireplace2"]
libraries = {
&"": SubResource("AnimationLibrary_03agl")
}

[node name="SpawnMonument" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -49)
texture = ExtResource("3_03agl")
hframes = 8

[node name="AnimationPlayer" type="AnimationPlayer" parent="SpawnMonument"]
libraries = {
&"": SubResource("AnimationLibrary_luem1")
}
speed_scale = 0.5

[node name="SpawnMonument2" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -37)
texture = ExtResource("4_03agl")
offset = Vector2(0, -12)
hframes = 8

[node name="AnimationPlayer" type="AnimationPlayer" parent="SpawnMonument2"]
libraries = {
&"": SubResource("AnimationLibrary_luem1")
}
speed_scale = 0.5

[node name="SpawnMonumentArea" type="Area2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="SpawnMonumentArea"]
polygon = PackedVector2Array(-7, -25, 7, -25, 16, -34, 16, -47, 6, -56, -6, -56, -16, -47, -16, -35)

[node name="CollisionPolygon2D2" type="CollisionPolygon2D" parent="SpawnMonumentArea"]
polygon = PackedVector2Array(-29, -27, -26, -25, -22, -25, -19, -27, -19, -32, -22, -35, -26, -35, -29, -32)

[node name="CollisionPolygon2D3" type="CollisionPolygon2D" parent="SpawnMonumentArea"]
position = Vector2(48, 0)
polygon = PackedVector2Array(-29, -27, -26, -25, -22, -25, -19, -27, -19, -32, -22, -35, -26, -35, -29, -32)

[node name="SpawnMonumentDestroyed" type="Sprite2D" parent="."]
visible = false
y_sort_enabled = true
position = Vector2(0, -37)
texture = ExtResource("5_iy0sv")
offset = Vector2(0, -12)

[node name="MonumentHealth" parent="." instance=ExtResource("4_iy0sv")]
visible = false
position = Vector2(0, -40)
scale = Vector2(1, 0.5625)
MarginLeft = 0
MarginRight = 0

[node name="EnemySpawnArea" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="EnemySpawnArea"]
position = Vector2(1.5, -8.5)
shape = SubResource("RectangleShape2D_ih68c")

[node name="Colliders" type="StaticBody2D" parent="." groups=["navigation_polygon_source_geometry_group"]]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="Colliders"]
polygon = PackedVector2Array(-48, 64, -55, 64, -64, 55, -63, -53, -54, -61, -49, -61, -49, -60, -54, -60, -62, -53, -62, 49, -54, 57, -48, 58)

[node name="CollisionPolygon2D2" type="CollisionPolygon2D" parent="Colliders"]
polygon = PackedVector2Array(48, 58, 48, 64, 55, 64, 64, 55, 64, -54, 54, -61, -49, -61, -49, -60, 54, -60, 63, -53, 62, 49, 53, 58)

[node name="CollisionPolygon2D3" type="CollisionPolygon2D" parent="Colliders"]
polygon = PackedVector2Array(-16, -35, -7, -25, 7, -25, 16, -34, 16, -47, 6, -56, -6, -56, -16, -47)

[node name="CollisionPolygon2D4" type="CollisionPolygon2D" parent="Colliders"]
polygon = PackedVector2Array(-29, -32, -26, -35, -21, -35, -19, -32, -19, -27, -22, -25, -26, -25, -29, -27)

[node name="CollisionPolygon2D5" type="CollisionPolygon2D" parent="Colliders"]
polygon = PackedVector2Array(19, -32, 22, -35, 26, -35, 29, -32, 29, -27, 26, -25, 22, -25, 19, -27)

[node name="CollisionShape2D" type="CollisionShape2D" parent="Colliders"]
position = Vector2(49, 36)
shape = SubResource("RectangleShape2D_03agl")

[node name="CollisionShape2D2" type="CollisionShape2D" parent="Colliders"]
position = Vector2(-48, 36)
shape = SubResource("RectangleShape2D_03agl")

[node name="CollisionPolygon2D6" type="CollisionPolygon2D" parent="Colliders"]
visible = false
polygon = PackedVector2Array(-4, -44, -2, -42, 2, -42, 4, -44, 4, -51, -4, -51)
disabled = true
