using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class Region7Manager : Node2D
{
	[Export] public float BaseSpawnInterval { get; set; } = 15.0f; // Base 15 seconds
	[Export] public int RegionId { get; set; } = 7;
	[Export] public int InitiallySpawnedObjects { get; set; } = 15;
	[Export] public int MaxItemsSpawned { get; set; } = 50;

	// Object spawn weights (higher = more likely to spawn)
	[Export] public int Tree2SpawnWeight { get; set; } = 25; // Reduced by 5 for Trunk
	[Export] public int CopperRockSpawnWeight { get; set; } = 15;
	[Export] public int GreenBush2SpawnWeight { get; set; } = 15;
	[Export] public int BrownMushroomSpawnWeight { get; set; } = 15;
	[Export] public int RockSpawnWeight { get; set; } = 10;
	[Export] public int Rock2SpawnWeight { get; set; } = 10;
	[Export] public int BerryBushSpawnWeight { get; set; } = 5;
	[Export] public int TrunkSpawnWeight { get; set; } = 5;

	// Scene references
	[Export] public PackedScene Tree2Scene { get; set; }
	[Export] public PackedScene CopperRockScene { get; set; }
	[Export] public PackedScene GreenBush2Scene { get; set; }
	[Export] public PackedScene BrownMushroomScene { get; set; }
	[Export] public PackedScene RockScene { get; set; }
	[Export] public PackedScene Rock2Scene { get; set; }
	[Export] public PackedScene BerryBushScene { get; set; }
	[Export] public PackedScene TrunkScene { get; set; }

	// Enemy spawning configuration
	[Export] public int MaxEnemiesPerRegion { get; set; } = 2;
	[Export] public float EnemySpawnInterval { get; set; } = 120.0f; // 2 minutes
	[Export] public PackedScene GoblinScene { get; set; }

	// Internal state
	private Timer _spawnTimer;
	private Timer _enemySpawnTimer;
	private Random _random = new();
	private bool _isRegionUnlocked = false;
	private CustomDataLayerManager _customDataManager;
	private Dictionary<Vector2I, Node2D> _activeObjects = new();
	private Dictionary<Vector2I, int> _objectHealthData = new();
	private List<BaseEnemy> _activeEnemies = new();
	private List<EnemyTerritory> _territories = new();

	public override void _Ready()
	{
		CheckRegionUnlockStatus();

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		// Always connect to region unlock signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked += OnRegionUnlocked;
		}

		// Always load existing data regardless of unlock status
		CallDeferred(nameof(CleanCorruptedData));
		CallDeferred(nameof(LoadExistingObjects));
		CallDeferred(nameof(LoadExistingEnemies));
		CallDeferred(nameof(LoadObjectHealthData));

		// Always do initial spawn (for visibility when region unlocks)
		CallDeferred(nameof(FirstTimeInitialize));

		// Find and register territories
		CallDeferred(nameof(FindTerritories));

		// StartOngoingSpawning will be called after LoadExistingObjects completes (if region is unlocked)
	}

	private void CheckRegionUnlockStatus()
	{
		_isRegionUnlocked = GameSaveData.Instance.UnlockedRegions.Contains(RegionId);
		GD.Print($"Region{RegionId}Manager: Region unlock status: {_isRegionUnlocked}");
	}

	private void OnRegionUnlocked(int regionId)
	{
		if (regionId == RegionId && !_isRegionUnlocked)
		{
			_isRegionUnlocked = true;
			GD.Print($"Region{RegionId}Manager: Region unlocked! Starting spawning systems.");
			StartOngoingSpawning();
		}
	}

	private void FindTerritories()
	{
		var regionSpecific = GetNode<Node2D>($"/root/world/RegionSpecific/Region{RegionId}");
		if (regionSpecific != null)
		{
			foreach (Node child in regionSpecific.GetChildren())
			{
				if (child is EnemyTerritory territory)
				{
					_territories.Add(territory);
					GD.Print($"Region{RegionId}Manager: Found territory at {territory.GlobalPosition}");
				}
			}
		}
		GD.Print($"Region{RegionId}Manager: Found {_territories.Count} territories");
	}

	private EnemyTerritory FindClosestTerritory(Vector2 position)
	{
		EnemyTerritory closest = null;
		float closestDistance = float.MaxValue;

		foreach (var territory in _territories)
		{
			if (!IsInstanceValid(territory)) continue;

			float distance = position.DistanceTo(territory.GlobalPosition);
			if (distance < closestDistance)
			{
				closestDistance = distance;
				closest = territory;
			}
		}

		return closest;
	}

	public void FirstTimeInitialize()
	{
		if(GameSaveData.Instance.FirstTimeInitializedRegions.Contains(RegionId)) return;

		// Spawn 6 Tree
		for(var i = 0; i < 6; i++) TrySpawnObject(ObjectType.Tree);

		// Spawn 6 CopperRock
		for(var i = 0; i < 6; i++) TrySpawnObject(ObjectType.CopperRock);

		// Spawn 3 GreenBush
		for(var i = 0; i < 3; i++) TrySpawnObject(ObjectType.GreenBush);

		GameSaveData.Instance.FirstTimeInitializedRegions.Add(RegionId);
	}

	private void OnSpawnTimer()
	{
		TrySpawnObject();

		_spawnTimer.WaitTime = CalculateNextSpawnInterval();
		_spawnTimer.Start();
	}

	private void OnEnemySpawnTimer()
	{
		TrySpawnEnemy();

		_enemySpawnTimer.WaitTime = EnemySpawnInterval;
		_enemySpawnTimer.Start();
	}

	private float CalculateNextSpawnInterval()
	{
		int objectCount = _activeObjects.Count;
		float nextInterval = BaseSpawnInterval + objectCount;

		GD.Print($"Region{RegionId}Manager: Next spawn in {nextInterval}s (base: {BaseSpawnInterval}s + {objectCount} active objects)");
		return nextInterval;
	}

	private void TrySpawnObject(ObjectType? objectType = null)
	{
		if (!_isRegionUnlocked) return;

		// Check if we've reached the maximum number of spawned objects
		if (_activeObjects.Count >= MaxItemsSpawned)
		{
			GD.Print($"Region{RegionId}Manager: Maximum objects reached ({_activeObjects.Count}/{MaxItemsSpawned})");
			return;
		}

		// Find a valid spawn position
		var validPositions = GetValidSpawnPositions();
		if (validPositions.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No valid spawn positions available");
			return;
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		var selectedObjectType = objectType ?? DetermineObjectTypeToSpawn();
		SpawnObjectAt(spawnPosition, selectedObjectType);
	}

	private void TrySpawnEnemy()
	{
		// Clean up dead enemies first
		int removedCount = _activeEnemies.RemoveAll(enemy => !IsInstanceValid(enemy));
		if (removedCount > 0)
		{
			UpdateEnemiesInGameData();
		}

		// Check if we're under the limit
		if (_activeEnemies.Count >= MaxEnemiesPerRegion)
		{
			GD.Print($"Region{RegionId}Manager: Enemy limit reached ({_activeEnemies.Count}/{MaxEnemiesPerRegion})");
			return;
		}

		// Find a random valid position for enemy spawning
		var validPositions = GetValidEnemySpawnPositions();
		if (validPositions.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No valid enemy spawn positions available");
			return;
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		SpawnEnemyAt(spawnPosition, EnemyType.Goblin);
	}

	private List<Vector2I> GetValidSpawnPositions()
	{
		var validPositions = new List<Vector2I>();

		// Get all tiles in this region where objects can be spawned
		var tiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanDestroyableObject &&
			tile.ObjectTypePlaced == ObjectTypePlaced.None);

		foreach (var tile in tiles)
		{
			if (!_activeObjects.ContainsKey(tile.Position))
			{
				validPositions.Add(tile.Position);
			}
		}

		return validPositions;
	}

	private List<Vector2I> GetValidEnemySpawnPositions()
	{
		var validPositions = new List<Vector2I>();

		// Get all tiles in this region where enemies can be spawned
		var tiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanEnemy &&
			tile.ObjectTypePlaced == ObjectTypePlaced.None);

		foreach (var tile in tiles)
		{
			validPositions.Add(tile.Position);
		}

		return validPositions;
	}

	private ObjectType DetermineObjectTypeToSpawn()
	{
		var weightedOptions = new List<(ObjectType type, int weight)>
		{
			(ObjectType.Tree, Tree2SpawnWeight),
			(ObjectType.CopperRock, CopperRockSpawnWeight),
			(ObjectType.GreenBush, GreenBush2SpawnWeight),
			(ObjectType.BrownMushroom, BrownMushroomSpawnWeight),
			(ObjectType.Rock, RockSpawnWeight),
			(ObjectType.Rock2, Rock2SpawnWeight),
			(ObjectType.BerryBush, BerryBushSpawnWeight),
			(ObjectType.Trunk, TrunkSpawnWeight)
		};

		int totalWeight = weightedOptions.Sum(option => option.weight);
		int randomValue = _random.Next(totalWeight);

		int currentWeight = 0;
		foreach (var (type, weight) in weightedOptions)
		{
			currentWeight += weight;
			if (randomValue < currentWeight)
			{
				return type;
			}
		}

		return ObjectType.Tree; // Fallback
	}

	private void SpawnObjectAt(Vector2I tilePosition, ObjectType objectType)
	{
		Node2D spawnedObject = null;
		ObjectTypePlaced placedType = ObjectTypePlaced.None;

		switch (objectType)
		{
			case ObjectType.Tree:
				spawnedObject = SpawnTree(tilePosition);
				placedType = ObjectTypePlaced.Tree;
				break;
			case ObjectType.CopperRock:
				spawnedObject = SpawnCopperRock(tilePosition);
				placedType = ObjectTypePlaced.Rock;
				break;
			case ObjectType.GreenBush:
				spawnedObject = SpawnGreenBush(tilePosition);
				placedType = ObjectTypePlaced.BerryBush;
				break;
			case ObjectType.BrownMushroom:
				spawnedObject = SpawnBrownMushroom(tilePosition);
				placedType = ObjectTypePlaced.BerryBush;
				break;
			case ObjectType.Rock:
				spawnedObject = SpawnRock(tilePosition);
				placedType = ObjectTypePlaced.Rock;
				break;
			case ObjectType.Rock2:
				spawnedObject = SpawnRock2(tilePosition);
				placedType = ObjectTypePlaced.Rock2;
				break;
			case ObjectType.BerryBush:
				spawnedObject = SpawnBerryBush(tilePosition);
				placedType = ObjectTypePlaced.BerryBush;
				break;
			case ObjectType.Trunk:
				spawnedObject = SpawnTrunk(tilePosition);
				placedType = ObjectTypePlaced.Rock;
				break;
		}

		if (spawnedObject != null)
		{
			_activeObjects[tilePosition] = spawnedObject;
			_customDataManager.SetObjectPlaced(tilePosition, placedType);
			SaveCustomLayerDataToGameData();
		}
	}

	private void SpawnEnemyAt(Vector2I tilePosition, EnemyType enemyType)
{
	if (GoblinScene == null)
	{
		GD.PrintErr("Region7Manager: GoblinScene is null!");
		return;
	}

	var enemy = GoblinScene.Instantiate<MeleeGoblin>();
	if (enemy == null)
	{
		GD.PrintErr("Region7Manager: Failed to instantiate goblin!");
		return;
	}

	// Set enemy properties
	enemy.SetRegion(RegionId);
	enemy.GlobalPosition = new Vector2(tilePosition.X * 16 + 8, tilePosition.Y * 16 + 8);

	// Find the closest territory and assign enemy to it
	var closestTerritory = FindClosestTerritory(enemy.GlobalPosition);
	if (closestTerritory != null)
	{
		closestTerritory.AssignEnemy(enemy);
		GD.Print($"Region{RegionId}Manager: Assigned goblin to territory at {closestTerritory.GlobalPosition}");
	}
	else
	{
		// Fallback: set territory center to spawn position
		enemy.SetTerritory(enemy.GlobalPosition, 120.0f);
		GD.Print($"Region{RegionId}Manager: No territory found, using spawn position as territory center");
	}

	// Add to scene
	GetParent().CallDeferred("add_child", enemy);

	// Track the enemy
	_activeEnemies.Add(enemy);
	UpdateEnemiesInGameData();

	GD.Print($"Region{RegionId}Manager: Spawned {enemyType} at {tilePosition} (Total: {_activeEnemies.Count}/{MaxEnemiesPerRegion})");
}

private void StartOngoingSpawning()
{
	if (!_isRegionUnlocked) return;

	_spawnTimer = new Timer();
	_spawnTimer.WaitTime = CalculateNextSpawnInterval();
	_spawnTimer.OneShot = false;
	_spawnTimer.Timeout += OnSpawnTimer;
	AddChild(_spawnTimer);
	_spawnTimer.Start();

	_enemySpawnTimer = new Timer();
	_enemySpawnTimer.WaitTime = EnemySpawnInterval;
	_enemySpawnTimer.OneShot = false;
	_enemySpawnTimer.Timeout += OnEnemySpawnTimer;
	AddChild(_enemySpawnTimer);
	_enemySpawnTimer.Start();

	GD.Print($"Region{RegionId}Manager: Started ongoing spawning systems");
}

private void LoadExistingObjects()
{
	// Load object health data from CustomLayerData
	string healthKey = $"object_health_region_{RegionId}";
	if (GameSaveData.Instance.WorldData.CustomLayerData.ContainsKey(healthKey))
	{
		var healthData = GameSaveData.Instance.WorldData.CustomLayerData[healthKey];
		if (healthData is Dictionary<Vector2I, int> healthDict)
		{
			_objectHealthData = new Dictionary<Vector2I, int>(healthDict);
		}
	}

	GD.Print($"Region{RegionId}Manager: Loaded health data for {_objectHealthData.Count} objects");

	if (_isRegionUnlocked)
	{
		CallDeferred(nameof(StartOngoingSpawning));
	}
}

private void LoadExistingEnemies()
{
	// Load enemy data from CustomLayerData
	string enemyKey = $"enemies_region_{RegionId}";
	if (GameSaveData.Instance.WorldData.CustomLayerData.ContainsKey(enemyKey))
	{
		var enemyData = GameSaveData.Instance.WorldData.CustomLayerData[enemyKey];
		if (enemyData is List<EnemySaveData> enemyList)
		{
			foreach (var enemy in enemyList)
			{
				var tilePosition = new Vector2I((int)(enemy.Position.X / 16), (int)(enemy.Position.Y / 16));
				SpawnEnemyAt(tilePosition, enemy.EnemyType);
			}
			GD.Print($"Region{RegionId}Manager: Loaded {enemyList.Count} existing enemies");
		}
	}
}

private void LoadObjectHealthData()
{
	// This is now handled in LoadExistingObjects method
	// Keep this method for compatibility but it does nothing
}

private void CleanCorruptedData()
{
	// Clean up corrupted health data
	string healthKey = $"object_health_region_{RegionId}";
	if (GameSaveData.Instance.WorldData.CustomLayerData.ContainsKey(healthKey))
	{
		var healthData = GameSaveData.Instance.WorldData.CustomLayerData[healthKey];
		if (healthData is Dictionary<Vector2I, int> healthDict)
		{
			var objectsToRemove = new List<Vector2I>();

			foreach (var kvp in healthDict)
			{
				var position = kvp.Key;
				var tileData = _customDataManager.GetTileData(position);

				if (tileData.Region != RegionId)
				{
					GD.Print($"Region{RegionId}Manager: Removing corrupted object at {position} (belongs to region {tileData.Region})");
					objectsToRemove.Add(position);
				}
			}

			foreach (var position in objectsToRemove)
			{
				healthDict.Remove(position);
			}

			if (objectsToRemove.Count > 0)
			{
				GD.Print($"Region{RegionId}Manager: Cleaned {objectsToRemove.Count} corrupted objects");
				GameSaveData.Instance.WorldData.CustomLayerData[healthKey] = healthDict;
			}
		}
	}
}

private void SaveCustomLayerDataToGameData()
{
	var resourcesManager = ResourcesManager.Instance;
	if (resourcesManager != null)
	{
		resourcesManager.SaveCustomLayerData(_customDataManager);
	}
}

private void UpdateEnemiesInGameData()
{
	// Clean up dead enemies first
	_activeEnemies.RemoveAll(enemy => !IsInstanceValid(enemy));

	var enemySaveDataList = new List<EnemySaveData>();

	foreach (var enemy in _activeEnemies)
	{
		if (enemy.GetRegion() == RegionId)
		{
			enemySaveDataList.Add(enemy.GetSaveData());
		}
	}

	// Update GameData directly
	string key = $"enemies_region_{RegionId}";
	GameSaveData.Instance.WorldData.CustomLayerData[key] = enemySaveDataList;

	GD.Print($"Region{RegionId}Manager: Updated {enemySaveDataList.Count} enemies for region {RegionId} in GameData");
}

private Tree SpawnTree(Vector2I tilePosition)
{
	if (Tree2Scene == null) return null;

	var tree = Tree2Scene.Instantiate<Tree>();
	if (tree == null) return null;

	tree.SetTilePosition(tilePosition);
	GetParent().CallDeferred("add_child", tree);
	tree.TreeDestroyed += OnObjectDestroyed;
	RestoreObjectHealth(tilePosition, tree);

	return tree;
}

private CopperRock SpawnCopperRock(Vector2I tilePosition)
{
	if (CopperRockScene == null) return null;

	var copperRock = CopperRockScene.Instantiate<CopperRock>();
	if (copperRock == null) return null;

	copperRock.SetTilePosition(tilePosition);
	GetParent().CallDeferred("add_child", copperRock);
	copperRock.CopperRockDestroyed += OnObjectDestroyed;
	RestoreObjectHealth(tilePosition, copperRock);

	return copperRock;
}

private GreenBush SpawnGreenBush(Vector2I tilePosition)
{
	if (GreenBush2Scene == null) return null;

	var greenBush = GreenBush2Scene.Instantiate<GreenBush>();
	if (greenBush == null) return null;

	greenBush.SetTilePosition(tilePosition);
	GetParent().CallDeferred("add_child", greenBush);
	greenBush.GreenBushDestroyed += OnObjectDestroyed;
	RestoreObjectHealth(tilePosition, greenBush);

	return greenBush;
}

private BrownMushroom SpawnBrownMushroom(Vector2I tilePosition)
{
	if (BrownMushroomScene == null) return null;

	var mushroom = BrownMushroomScene.Instantiate<BrownMushroom>();
	if (mushroom == null) return null;

	mushroom.SetTilePosition(tilePosition);
	GetParent().CallDeferred("add_child", mushroom);
	mushroom.BrownMushroomDestroyed += OnObjectDestroyed;
	RestoreObjectHealth(tilePosition, mushroom);

	return mushroom;
}

private Rock SpawnRock(Vector2I tilePosition)
{
	if (RockScene == null) return null;

	var rock = RockScene.Instantiate<Rock>();
	if (rock == null) return null;

	rock.SetTilePosition(tilePosition);
	GetParent().CallDeferred("add_child", rock);
	rock.RockDestroyed += OnObjectDestroyed;
	RestoreObjectHealth(tilePosition, rock);

	return rock;
}

private Rock2 SpawnRock2(Vector2I tilePosition)
{
	if (Rock2Scene == null) return null;

	var rock = Rock2Scene.Instantiate<Rock2>();
	if (rock == null) return null;

	rock.SetTilePosition(tilePosition);
	GetParent().CallDeferred("add_child", rock);
	rock.Rock2Destroyed += OnObjectDestroyed;
	RestoreObjectHealth(tilePosition, rock);

	return rock;
}

private BerryBush SpawnBerryBush(Vector2I tilePosition)
{
	if (BerryBushScene == null) return null;

	var berryBush = BerryBushScene.Instantiate<BerryBush>();
	if (berryBush == null) return null;

	berryBush.SetTilePosition(tilePosition);
	GetParent().CallDeferred("add_child", berryBush);
	berryBush.BerryBushDestroyed += OnObjectDestroyed;
	RestoreObjectHealth(tilePosition, berryBush);

	return berryBush;
}

private Trunk SpawnTrunk(Vector2I tilePosition)
{
	if (TrunkScene == null) return null;

	var trunk = TrunkScene.Instantiate<Trunk>();
	if (trunk == null) return null;

	trunk.SetTilePosition(tilePosition);
	GetParent().CallDeferred("add_child", trunk);
	trunk.TrunkDestroyed += OnObjectDestroyed;
	RestoreObjectHealth(tilePosition, trunk);

	return trunk;
}

private void OnObjectDestroyed(Vector2I tilePosition)
{
	if (_activeObjects.ContainsKey(tilePosition))
	{
		_activeObjects.Remove(tilePosition);
		UpdateObjectHealthInGameData();
	}

	_customDataManager.SetObjectPlaced(tilePosition, ObjectTypePlaced.None);
	SaveCustomLayerDataToGameData();
}

private void RestoreObjectHealth(Vector2I tilePosition, IDestroyableObject destroyableObject)
{
	if (_objectHealthData.ContainsKey(tilePosition))
	{
		int savedHealth = _objectHealthData[tilePosition];
		destroyableObject.SetCurrentHealth(savedHealth);
	}
}

private void UpdateObjectHealthInGameData()
{
	// Collect current health from all active objects
	_objectHealthData.Clear();

	foreach (var kvp in _activeObjects)
	{
		var position = kvp.Key;
		var obj = kvp.Value;

		if (obj is IDestroyableObject destroyableObj)
		{
			_objectHealthData[position] = destroyableObj.GetCurrentHealth();
		}
	}

	// Update GameData directly
	string key = $"object_health_region_{RegionId}";
	GameSaveData.Instance.WorldData.CustomLayerData[key] = _objectHealthData;

	GD.Print($"Region{RegionId}Manager: Updated health data for {_objectHealthData.Count} objects in GameData");
}
}
