<guidance>
Implement following tasks. Make sure you don't change (break) any enum numerations.
It is very important that you understand deeply our system so that you don't make mistakes - read all required files (tscn and cs - both) so that you understand how it all works. Implement code only when you fully understand it (both cs and tscn) - even if you would need to read many files. You need to be 100% sure that you understand it before you implement it so that it works at first try.
Don't add comment.
Build project when you end to make sure it compiles.
</guidance>


TASK-1: We have iron key - ResourceType. I want you to change it's name to <PERSON><PERSON><PERSON> (leave same number of enum). Then adjust texture manager. Then adjust ItemInformation. Then adjust translations. Icon should be the same in texture manager as currently for iron key.

TASK-2: As we change iron key to stone key - you need to adjust it in Chest.cs - set that stone chest needs stone key to open.

TASK-3: When i start the game - rabbit spawn in region 1 with a given position and it stays with this frame, without moving, without idle animation - he only "activates" when i hit it. Fix it so that rabbit "lives" when i start the game. He seems to be stuck.

TASK-4: Look at region5manager - we have 2 enemies spawned there. Now, I want you to duplicate region5manager and call it region7manager. It should also spawn 2 enemies there. We would need to duplicate world->RegionSpecific->Region5->GoblinTerritory1 to Region7 (verify how it works and if it will work if we duplicate it). In region 7 we should have the same possible spawn objects as in region 5. Also, in world.tscn - add Region7Manager to RegionManagers node.

TASK-5: I would like to add a system where player "speaks" something like "i would need a wooden key" etc. So in order to do so, i added in player.tscn->CanvasLayer->Control->PlayerSpeak->Label. Initially label should be disabled. In player controller i want to add some kind of public const string that should be null initially and when we want to show some text then we should set this const to some value and it should be shown in label. But show in following way: i will add a translation key to this const string, and you need to 1) translate it (Tr()) then show it letter by letter, adding a letter every 0.05s, so that it looks like text is being typed. After text ends showing (last letter) - you need to wait 1s and then fade out text (fade out label over 1s) and then make label invisible (and restore const string to null and fade to non faded). Also, if you start typing text and then i change const string to other text - you need to stop typing current text and start typing new text.

TASK-6: When player does not have a key to open a chest - in chest script, set this previously added label to show text that player needs a key of given type - we need translations for all key types.

TASK-7: In regions 5-7 manager, understand in depth how items (like tree, rock etc) are spawned and make sure that there is no possibility to spawn 2 items in the same tile. Read how it is done in region 1 manager and how save/load/verification if can spawn on given tile is done and make sure it works as expected.