using Godot;
using System;

public partial class BigGreenBush : Node2D, IDestroyableObject
{
	[Export] public int MaxHealth { get; set; } = 6;
	[Export] public int PickaxeDamage { get; set; } = 2;

	[Signal] public delegate void BigGreenBushDestroyedEventHandler(Vector2I tilePosition);

	private int _currentHealth;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;

	private readonly Color _hitColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f;

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		AddToGroup("big_green_bushes");

		_sprite = GetNode<Sprite2D>("Sprite2D");
		if (_sprite == null)
		{
			GD.PrintErr("BigGreenBush: Sprite2D node not found!");
			return;
		}

		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("BigGreenBush: ProgressBar node not found!");
		}

		YSortEnabled = true;

		_sprite.Position = new Vector2(0, 8);

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("BigGreenBush: CustomDataLayerManager not found!");
		}

		if (_tilePosition == Vector2I.Zero)
		{
			_tilePosition = new Vector2I((int)(GlobalPosition.X - 8) / 16, (int)(GlobalPosition.Y - 8) / 16);
			_customDataManager?.SetObjectPlaced(_tilePosition, ObjectTypePlaced.Tree);
		}

		UpdateHPBar();

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	public override void _Process(double delta)
	{
		UpdateTransparency();
	}

	private void UpdateTransparency()
	{
		if (_sprite == null) return;

		var player = GetNode<PlayerController>("/root/world/Player");
		if (player == null) return;

		bool playerBehind = player.GlobalPosition.Y < GlobalPosition.Y + 8;
		float horizontalDistance = Math.Abs(player.GlobalPosition.X - GlobalPosition.X);
		bool closeEnoughHorizontally = horizontalDistance <= 16.0f;
		float verticalDistance = GlobalPosition.Y - player.GlobalPosition.Y;
		bool notTooFarAbove = verticalDistance <= 16.0f;

		var modulate = _sprite.Modulate;
		modulate.A = (playerBehind && closeEnoughHorizontally && notTooFarAbove) ? 0.5f : 1.0f;
		_sprite.Modulate = modulate;
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		UpdateHPBar();
		PlayHitAnimation();

		if (_currentHealth <= 0)
		{
			DestroyBigGreenBush();
		}
	}

	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		var originalScale = _sprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_sprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	private void DestroyBigGreenBush()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		DropResources();
		_customDataManager?.ClearObjectPlaced(_tilePosition);
		CommonSignals.Instance?.EmitAddXp(5);
		EmitSignal(SignalName.BigGreenBushDestroyed, _tilePosition);
		QueueFree();
	}

	private void DropResources()
	{
		for (int i = 0; i < 3; i++)
		{
			Vector2 offset = new Vector2(
				(float)(GD.Randf() - 0.5f) * 8.0f,
				(float)(GD.Randf() - 0.5f) * 8.0f
			);
			Vector2 spawnPosition = GlobalPosition + offset;
			DroppedResource.SpawnResource(spawnPosition, ResourceType.Leaf, 1);
		}

		Vector2 woodOffset = new Vector2(
			(float)(GD.Randf() - 0.5f) * 8.0f,
			(float)(GD.Randf() - 0.5f) * 8.0f
		);
		Vector2 woodSpawnPosition = GlobalPosition + woodOffset;
		DroppedResource.SpawnResource(woodSpawnPosition, ResourceType.Wood, 1);

		Vector2 branchOffset = new Vector2(
			(float)(GD.Randf() - 0.5f) * 8.0f,
			(float)(GD.Randf() - 0.5f) * 8.0f
		);
		Vector2 branchSpawnPosition = GlobalPosition + branchOffset;
		DroppedResource.SpawnResource(branchSpawnPosition, ResourceType.Branch, 1);
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	public void SetTilePosition(Vector2I position)
	{
		_tilePosition = position;
		GlobalPosition = new Vector2(position.X * 16 + 8, position.Y * 16 + 8 - 16);
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		var distance = _tilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		float healthPercentage = (float)_currentHealth / MaxHealth;

		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (_tilePosition == tilePosition)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			if (CanBeHitFrom(playerTile))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}
}
