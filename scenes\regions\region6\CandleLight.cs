using Godot;

public partial class CandleLight : Node2D
{
	[Export] public float LightRadius { get; set; } = 48.0f;
	[Export] public Color LightColor { get; set; } = new Color(1.0f, 0.9f, 0.7f, 1.0f); // Warm candle light
	[Export] public float LightEnergy { get; set; } = 0.8f;
	[Export] public bool OnlyShowAtNight { get; set; } = true;
	[Export] public float FlickerIntensity { get; set; } = 0.15f;
	[Export] public float FlickerSpeed { get; set; } = 3.0f;

	private Light2D _light;
	private DayNightManager _dayNightManager;
	private float _baseEnergy;
	private float _flickerTime = 0.0f;
	private GraveyardCandle _graveyardCandle;

	public override void _Ready()
	{
		// Get the Light2D node from the scene
		_light = GetParent().GetNode<Light2D>("CandleLight");
		if (_light == null)
		{
			GD.PrintErr("CandleLight: Light2D node not found in scene!");
			return;
		}

		_light.Enabled = true;
		_light.Energy = LightEnergy;
		_light.Color = LightColor;
		_light.Scale = Vector2.One * (LightRadius / 64.0f); // Scale the light

		_baseEnergy = LightEnergy;

		// Find the DayNightManager
		_dayNightManager = GetNode<DayNightManager>("/root/world/DayNight");
		if (_dayNightManager == null)
		{
			// Try alternative path
			_dayNightManager = GetTree().GetFirstNodeInGroup("DayNightManager") as DayNightManager;
			if (_dayNightManager == null)
			{
				GD.PrintErr("CandleLight: DayNightManager not found!");
			}
		}

		// Get reference to the graveyard candle
		_graveyardCandle = GetParent() as GraveyardCandle;

		GD.Print($"CandleLight: Initialized with radius {LightRadius}");
	}

	public override void _Process(double delta)
	{
		if (_light == null || _dayNightManager == null) return;

		// Only show light if candle is lit
		bool shouldShowLight = _graveyardCandle != null && _graveyardCandle.IsLit();
		
		// Show/hide light based on day/night cycle and candle state
		if (OnlyShowAtNight && shouldShowLight)
		{
			_light.Visible = _dayNightManager.IsNight();
		}
		else if (shouldShowLight)
		{
			_light.Visible = true;
		}
		else
		{
			_light.Visible = false;
		}

		// Add flickering effect for candle
		if (_light.Visible)
		{
			_flickerTime += (float)delta * FlickerSpeed;
			float flicker = Mathf.Sin(_flickerTime) * FlickerIntensity;
			_light.Energy = _baseEnergy + flicker;
		}
	}

	public void SetLightRadius(float radius)
	{
		LightRadius = radius;
		if (_light != null)
		{
			_light.Scale = Vector2.One * (radius / 64.0f);
		}
		GD.Print($"CandleLight: Radius set to {radius}");
	}

	public void SetLightColor(Color color)
	{
		LightColor = color;
		if (_light != null)
		{
			_light.Color = color;
		}
	}

	public void SetLightEnergy(float energy)
	{
		LightEnergy = energy;
		_baseEnergy = energy;
		if (_light != null)
		{
			_light.Energy = energy;
		}
	}
}
