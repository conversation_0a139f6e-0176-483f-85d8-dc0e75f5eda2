Implement following tasks:

TASK-1: In HouseInterior - when panel is visible and player clicks Escape action (my action defined in input map - currently as ESC button) - then hide panel (invoke HidePanel method).

TASK-2: In PlayerController.cs - remove Input.IsKeyPressed(Key.E) from useTool detection and mouse button right detection. Instead, use my action that i defined in input map - it is called UseTool (currently set to right mouse button - should be active when action is pressed just like mouse button now).

TASK-3: Find all places (mostly in panels) when I use Key.R to interact - and change it to my action defined in input map - it is called Interact (currently set to E button).

TASK-4: The same for ESC key - find places where it is used and change it to my action defined in input map - it is called Escape (currently set to ESC button).