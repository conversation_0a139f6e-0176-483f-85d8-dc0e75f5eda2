TASK-1: Duplicate rock tscn and cs and create Barrel (cs, tscn). Do not create it from scratch, duplicate 1:1 from rock. Then change begaviour to following: when destroyed, barrel spawns 1 plank + 1 gold coin (see how DroppedResource.cs). Barrel has 9 hp. Barrel will be 1x1 but it's sprite is 48x16 - you need to use sprite with animation property set properly (it has 3 frames). So when barrel has 9-7hp - show sprite at position 0 (i mean animation), when 6-4hp - show sprite at position 1, when 3-0hp - show sprite at position 2. You also need to show ProgressBar which is a hp - just like in rock tscn (show only when not full hp). I will set sprite.

TASK-2: Duplicate Barrel (cs, tscn) 1:1 and create DestroyableChest - same logic, spawn 1 plank, 1 gold coin. It also has animation (3 frames) and hp (9) and progress bar. I will set sprite.

TASK-3: Verify that bridge can only be build at place that CanBridge = true (this is only requirement for bridge to build for now).