using Godot;
using System;
using System.Text.Json;
using System.Text.Json.Serialization;

/// <summary>
/// Global JSON serialization options with all necessary converters
/// </summary>
public static class GlobalJsonOptions
{
    /// <summary>
    /// Standard JSON options for all game save/load operations
    /// </summary>
    public static JsonSerializerOptions Default { get; } = new JsonSerializerOptions
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        Converters = {
            new Vector2JsonConverter(),
            new Vector2IJsonConverter(),
            new EnemyTypeJsonConverter(),
            new EnemyStateJsonConverter(),
            new EnemyBehaviorTypeJsonConverter(),
            new RabbitStateJsonConverter(),
            new RabbitDirectionJsonConverter()
        }
    };
}

/// <summary>
/// JSON converter for Vector2 that handles camelCase property names
/// </summary>
public class Vector2JsonConverter : JsonConverter<Vector2>
{
    public override Vector2 Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.StartObject)
        {
            // Handle object format: {"x": 1.0, "y": 2.0}
            float x = 0, y = 0;

            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndObject)
                {
                    return new Vector2(x, y);
                }

                if (reader.TokenType == JsonTokenType.PropertyName)
                {
                    var propertyName = reader.GetString()?.ToLowerInvariant();
                    reader.Read();

                    switch (propertyName)
                    {
                        case "x":
                            x = reader.GetSingle();
                            break;
                        case "y":
                            y = reader.GetSingle();
                            break;
                    }
                }
            }
        }

        throw new JsonException("Unable to parse Vector2");
    }

    public override void Write(Utf8JsonWriter writer, Vector2 value, JsonSerializerOptions options)
    {
        writer.WriteStartObject();
        writer.WriteNumber("x", value.X);
        writer.WriteNumber("y", value.Y);
        writer.WriteEndObject();
    }
}

/// <summary>
/// JSON converter for EnemyType enum that handles string values
/// </summary>
public class EnemyTypeJsonConverter : JsonConverter<EnemyType>
{
    public override EnemyType Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();

            // Handle the string values from JSON
            return stringValue?.ToLowerInvariant() switch
            {
                "goblin" => EnemyType.Goblin,
                "orc" => EnemyType.Orc,
                "skeleton" => EnemyType.Skeleton,
                "archer" => EnemyType.Archer,
                "mage" => EnemyType.Mage,
                "necromancer" => EnemyType.Necromancer,
                "shaman" => EnemyType.Shaman,
                "goblinking" => EnemyType.GoblinKing,
                "dragonlord" => EnemyType.DragonLord,
                _ => EnemyType.Goblin // Default fallback
            };
        }

        if (reader.TokenType == JsonTokenType.Number)
        {
            // Handle numeric values (enum index)
            var intValue = reader.GetInt32();
            return (EnemyType)intValue;
        }

        throw new JsonException($"Unable to parse EnemyType from {reader.TokenType}");
    }

    public override void Write(Utf8JsonWriter writer, EnemyType value, JsonSerializerOptions options)
    {
        // Write as string for better readability in JSON
        writer.WriteStringValue(value.ToString());
    }
}

/// <summary>
/// JSON converter for EnemyState enum
/// </summary>
public class EnemyStateJsonConverter : JsonConverter<EnemyState>
{
    public override EnemyState Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            return stringValue?.ToLowerInvariant() switch
            {
                "patrolling" => EnemyState.Patrolling,
                "pursuing" => EnemyState.Pursuing,
                "attacking" => EnemyState.Attacking,
                "returning" => EnemyState.Returning,
                "stunned" => EnemyState.Stunned,
                "dead" => EnemyState.Dead,
                _ => EnemyState.Patrolling // Default fallback
            };
        }
        
        if (reader.TokenType == JsonTokenType.Number)
        {
            var intValue = reader.GetInt32();
            return (EnemyState)intValue;
        }
        
        throw new JsonException($"Unable to parse EnemyState from {reader.TokenType}");
    }

    public override void Write(Utf8JsonWriter writer, EnemyState value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}

/// <summary>
/// JSON converter for EnemyBehaviorType enum
/// </summary>
public class EnemyBehaviorTypeJsonConverter : JsonConverter<EnemyBehaviorType>
{
    public override EnemyBehaviorType Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            return stringValue?.ToLowerInvariant() switch
            {
                "territorial" => EnemyBehaviorType.Territorial,
                "aggressive" => EnemyBehaviorType.Aggressive,
                _ => EnemyBehaviorType.Territorial // Default fallback
            };
        }

        if (reader.TokenType == JsonTokenType.Number)
        {
            var intValue = reader.GetInt32();
            return (EnemyBehaviorType)intValue;
        }

        throw new JsonException($"Unable to parse EnemyBehaviorType from {reader.TokenType}");
    }

    public override void Write(Utf8JsonWriter writer, EnemyBehaviorType value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}

/// <summary>
/// JSON converter for RabbitState enum
/// </summary>
public class RabbitStateJsonConverter : JsonConverter<Rabbit.RabbitState>
{
    public override Rabbit.RabbitState Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            return stringValue?.ToLowerInvariant() switch
            {
                "idle" => Rabbit.RabbitState.Idle,
                "moving" => Rabbit.RabbitState.Moving,
                "sleeping" => Rabbit.RabbitState.Sleeping,
                "fleeing" => Rabbit.RabbitState.Fleeing,
                "searching" => Rabbit.RabbitState.Searching,
                _ => Rabbit.RabbitState.Idle // Default fallback
            };
        }
        
        if (reader.TokenType == JsonTokenType.Number)
        {
            var intValue = reader.GetInt32();
            return (Rabbit.RabbitState)intValue;
        }
        
        throw new JsonException($"Unable to parse RabbitState from {reader.TokenType}");
    }

    public override void Write(Utf8JsonWriter writer, Rabbit.RabbitState value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}

/// <summary>
/// JSON converter for Rabbit.Direction enum
/// </summary>
public class RabbitDirectionJsonConverter : JsonConverter<Rabbit.Direction>
{
    public override Rabbit.Direction Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            return stringValue?.ToLowerInvariant() switch
            {
                "up" => Rabbit.Direction.Up,
                "down" => Rabbit.Direction.Down,
                "left" => Rabbit.Direction.Left,
                "right" => Rabbit.Direction.Right,
                _ => Rabbit.Direction.Up // Default fallback
            };
        }

        if (reader.TokenType == JsonTokenType.Number)
        {
            var intValue = reader.GetInt32();
            return (Rabbit.Direction)intValue;
        }

        throw new JsonException($"Unable to parse Rabbit.Direction from {reader.TokenType}");
    }

    public override void Write(Utf8JsonWriter writer, Rabbit.Direction value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}
