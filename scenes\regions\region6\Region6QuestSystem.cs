using Godot;
using System.Collections.Generic;

public partial class Region6QuestSystem : Node2D
{
	private List<GraveyardCandle> _candles = new List<GraveyardCandle>();
	private AnimationPlayer _mainAnimationPlayer;
	private Node2D _animatorsNode;
	private Node2D _sculptureRegion6;
	private AnimationPlayer _sculptureAnimationPlayer;
	private List<Node2D> _candleEffects = new List<Node2D>();
	private List<AnimationPlayer> _candleEffectAnimationPlayers = new List<AnimationPlayer>();
	private bool _puzzleCompleted = false;

	public override void _Ready()
	{
		// Get all candle nodes
		for (int i = 1; i <= 8; i++)
		{
			var candle = GetNode<GraveyardCandle>($"GraveyardCandle{i}");
			if (candle != null)
			{
				candle.CandleId = i;
				_candles.Add(candle);
			}
			else
			{
				GD.PrintErr($"Region6QuestSystem: GraveyardCandle{i} not found!");
			}
		}
		
		GetNode<Sprite2D>("Animators/Explosion").Visible = false;
		GetNode<Sprite2D>("Animators/Explosion2").Visible = false;
		// Get animation components
		_mainAnimationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_animatorsNode = GetNode<Node2D>("Animators");
		_sculptureRegion6 = GetNode<Node2D>("SculptureRegion6");
		
		if (_sculptureRegion6 != null)
		{
			_sculptureAnimationPlayer = _sculptureRegion6.GetNode<AnimationPlayer>("AnimationPlayer");
		}

		// Get candle effect nodes
		for (int i = 1; i <= 8; i++)
		{
			var candleEffect = _animatorsNode?.GetNode<Node2D>($"Candle{i}Effect");
			if (candleEffect != null)
			{
				_candleEffects.Add(candleEffect);
				var animPlayer = candleEffect.GetNode<AnimationPlayer>("AnimationPlayer");
				if (animPlayer != null)
				{
					_candleEffectAnimationPlayers.Add(animPlayer);
				}
				
				// Initially make candle effects invisible
				candleEffect.Visible = false;
			}
		}

		// Connect to candle interaction signal
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.GraveyardCandleInteracted += OnCandleInteracted;
		}

		// Load saved state with a small delay to ensure all nodes are ready
		CallDeferred(MethodName.LoadCandleStates);
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.GraveyardCandleInteracted -= OnCandleInteracted;
		}
	}

	private void OnCandleInteracted(int candleId)
	{
		GD.Print($"Region6QuestSystem: Candle {candleId} interacted");

		// Update save data
		if (candleId >= 1 && candleId <= 8)
		{
			GameSaveData.Instance.Region6CandleStates[candleId - 1] = true;
			GD.Print($"Region6QuestSystem: Saved candle {candleId} state as lit");
			ResourcesManager.Instance?.ForceSave();
		}

		// Check if all candles are lit
		CheckPuzzleCompletion();
	}

	private void CheckPuzzleCompletion()
	{
		if (_puzzleCompleted) return;

		bool allLit = true;
		for (int i = 0; i < 8; i++)
		{
			if (!GameSaveData.Instance.Region6CandleStates[i])
			{
				allLit = false;
				break;
			}
		}

		if (allLit)
		{
			_puzzleCompleted = true;
			StartUnlockSequence();
		}
	}

	private void StartUnlockSequence()
	{
		GD.Print("Region6QuestSystem: Starting unlock sequence!");

		// Make candle effects visible and play their animations
		for (int i = 0; i < _candleEffects.Count; i++)
		{
			if (_candleEffects[i] != null)
			{
				_candleEffects[i].Visible = true;
				if (i < _candleEffectAnimationPlayers.Count && _candleEffectAnimationPlayers[i] != null)
				{
					_candleEffectAnimationPlayers[i].Play("Play");
				}
			}
		}

		// Play main unlock animation
		if (_mainAnimationPlayer != null)
		{
			_mainAnimationPlayer.Play("Unlock");
			_mainAnimationPlayer.AnimationFinished += OnUnlockAnimationFinished;
		}
	}

	private void OnUnlockAnimationFinished(StringName animName)
	{
		if (animName == "Unlock")
		{
			_mainAnimationPlayer.AnimationFinished -= OnUnlockAnimationFinished;
			
			// Play sculpture animation
			if (_sculptureAnimationPlayer != null)
			{
				_sculptureAnimationPlayer.Play("On");
			}
			
			GetNode<Sprite2D>("Animators/Explosion").QueueFree();
			GetNode<Sprite2D>("Animators/Explosion2").QueueFree();
		}
	}

	private void LoadCandleStates()
	{
		var candleStates = GameSaveData.Instance.Region6CandleStates;

		// Ensure we have 8 states
		while (candleStates.Count < 8)
		{
			candleStates.Add(false);
		}

		GD.Print($"Region6QuestSystem: Loading candle states: [{string.Join(", ", candleStates)}]");

		bool allLit = true;
		for (int i = 0; i < 8; i++)
		{
			if (i < _candles.Count && _candles[i] != null)
			{
				_candles[i].SetLit(candleStates[i]);
				GD.Print($"Region6QuestSystem: Set candle {i + 1} to {(candleStates[i] ? "lit" : "unlit")}");
			}

			if (!candleStates[i])
			{
				allLit = false;
			}
		}

		// If all candles are lit on load, restore completed state
		if (allLit)
		{
			_puzzleCompleted = true;
			GD.Print("Region6QuestSystem: All candles lit on load, restoring completed state");

			// Play sculpture animation
			if (_sculptureAnimationPlayer != null)
			{
				_sculptureAnimationPlayer.Play("On");
			}

			// Queue free the animators node as it's no longer needed
			if (_animatorsNode != null)
			{
				_animatorsNode.QueueFree();
			}
		}
	}
}
