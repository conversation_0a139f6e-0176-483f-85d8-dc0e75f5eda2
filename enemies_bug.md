# Enemy Pursuit and Territory Bug

## Problem Description

There is a critical bug in the enemy AI that incorrectly restricts their movement during pursuit, preventing them from behaving as designed in `enemies_system.md`. Both `Territorial` and `Aggressive` enemies are affected.

### Incorrect Behavior
- **Territorial Enemies**: When a player enters their territory, they start pursuing. However, the moment the enemy steps outside its `TerritoryRadius` (120 units), it immediately stops the chase and enters the `Returning` state. This happens even if the player is still well within the enemy's `_pursuitRange` (200 units).
- **Aggressive Enemies**: These enemies are meant to hunt targets across the map with no territory restrictions. The bug incorrectly leashes them to their spawn territory, causing them to abandon chases when they move too far from their territory center. This completely breaks their intended "offensive hunter" behavior.

### Expected Behavior (as per `enemies_system.md`)
- **Territorial Enemies**: Should be able to leave their home territory to pursue a target. They should only stop pursuing and return home if the target moves beyond their `_pursuitRange` or is defeated.
- **Aggressive Enemies**: Should pursue targets relentlessly across the map, without being constrained by any territory radius.

## Root Cause

The bug stems from incorrect checks in `MeleeGoblin.cs` that override the correct base logic from `BaseEnemy.cs`.

1.  **`MeleeGoblin.HandlePursuit()`**: Contains a check `if (ShouldReturnToTerritory())` that incorrectly forces the enemy to return home if it leaves its territory radius, even during an active chase.
2.  **`MeleeGoblin.OnTerritoryExited()`**: This method, connected to the `EnemyTerritory`'s `Area2D`, also incorrectly forces the goblin to return home the moment the player leaves the territory area, ignoring the enemy's own pursuit range.

These overrides prevent the correct pursuit logic in `BaseEnemy.cs` from functioning as intended.

---

## Navigation and Pathfinding Bug

### Problem Description

Enemies, particularly `Aggressive` ones, are supposed to use the `NavigationAgent2D` to navigate around static obstacles (like the `EnemySpawnRegion8` structure). However, they currently do not pathfind correctly. Instead, they walk directly into obstacles and get stuck, unable to reach their target.

### Expected Behavior

Aggressive enemies should calculate a path around any static colliders that are part of the navigation mesh and follow that path to their target.

2.  **`MeleeGoblin.OnTerritoryExited()`**: This method, connected to the `EnemyTerritory`'s `Area2D`, also incorrectly forces the goblin to return home the moment the player leaves the territory area, ignoring the enemy's own pursuit range.

These overrides prevent the correct pursuit logic in `BaseEnemy.cs` from functioning as intended.
---

## Navigation and Pathfinding Bug

### Problem Description

Enemies, particularly `Aggressive` ones, are supposed to use the `NavigationAgent2D` to navigate around static obstacles (like the `EnemySpawnRegion8` structure). However, they currently do not pathfind correctly. Instead, they walk directly into obstacles and get stuck.

### Expected Behavior

Aggressive enemies should calculate a path around any static colliders that are part of the navigation mesh and follow that path to their target.

### Root Cause

This issue is caused by a combination of a code logic flaw and a scene configuration problem:

1.  **Scene Configuration Issue**: The `StaticBody2D` nodes used for obstacles (like in `EnemySpawnRegion8`) are missing `NavigationPolygonInstance` children. The navigation system requires these nodes to define obstacle shapes for pathfinding. A `CollisionShape2D` is used for physics but is ignored by the navigation mesh generation process.
2.  **`BaseEnemy.HandleMovement()` Logic Flaw**: The movement code incorrectly uses `_navigationAgent.IsNavigationFinished()`. This method returns `true` when the agent reaches the end of a *path segment*, not the final destination. As a result, the enemy gives up on pathfinding as soon as it reaches the first corner of an obstacle and attempts to move in a straight line, getting stuck.
3.  **`BaseEnemy._Ready()` Node Creation**: The `NavigationAgent2D` is created programmatically in `BaseEnemy.cs`. This prevents easy configuration in the Godot editor. Moving this node into the `MeleeGoblin.tscn` scene is better practice.

### Solution / To-Do

To fix the navigation system, the following steps are required:

1.  **Add Navigation Obstacles to Scenes**:
    *   For every static obstacle in the game (e.g., `EnemySpawnRegion8.tscn`), open its scene file.
    *   Select the `StaticBody2D` node.
    *   Add a `NavigationPolygonInstance` node as a child.
    *   In the Inspector for the new node, set its `Mode` property to `Obstacle`.
    *   Using the "Create points" tool in the 2D viewport toolbar, draw a polygon that matches the shape of the obstacle's `CollisionShape2D`.

2.  **Refactor `NavigationAgent2D` Creation**:
    *   **In `BaseEnemy.cs`**: Remove the code in `SetupNavigationAgent()` that creates a `new NavigationAgent2D()`. Instead, get the node from the scene tree (e.g., `_navigationAgent = GetNode<NavigationAgent2D>("NavigationAgent2D");`).
    *   **In `MeleeGoblin.tscn`**: Add a `NavigationAgent2D` node as a child of the root `MeleeGoblin` node. Configure its properties (like `Radius`, `AvoidanceEnabled`) in the Inspector.

3.  **Correct the Movement Logic**:
    *   **In `BaseEnemy.cs`**: Modify the `HandleMovement()` method. The condition that checks if the agent should be used needs to be changed. Instead of `_navigationAgent.IsNavigationFinished() == false`, it should check if the agent's target is set and not yet reached. A better check is `!_navigationAgent.IsTargetReached()`. This ensures the enemy follows the complete path.

