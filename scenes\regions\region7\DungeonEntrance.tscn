[gd_scene load_steps=3 format=3 uid="uid://dna20kwegwn3d"]

[ext_resource type="Texture2D" uid="uid://diqxpdylreto1" path="res://resources/solaria/mesoamericaDunegeon/entrance_to_dungeon_5.png" id="1_j2s18"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_0ik47"]
size = Vector2(11, 9)

[node name="DungeonEntrance" type="Sprite2D"]
texture = ExtResource("1_j2s18")

[node name="PlayerDetector" type="Area2D" parent="."]
position = Vector2(0, 24)

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(-0.5, -24.5)
shape = SubResource("RectangleShape2D_0ik47")
