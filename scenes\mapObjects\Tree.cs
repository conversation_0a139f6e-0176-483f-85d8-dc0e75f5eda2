using Godot;
using System;

/// <summary>
/// Tree object that can be destroyed by pickaxe
/// Handles hit animations, health management, and resource dropping
/// </summary>
public partial class Tree : Node2D, IDestroyableObject
{
	[Export] public int MaxHealth { get; set; } = 8;
	[Export] public int PickaxeDamage { get; set; } = 2;
	[Export] public ResourceType ResourceType { get; set; } = ResourceType.Wood;
	[Export] public int ResourceAmount { get; set; } = 3;

	// Event for when tree is destroyed
	[Signal] public delegate void TreeDestroyedEventHandler(Vector2I tilePosition);

	private int _currentHealth;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;

	// Hit animation properties
	private readonly Color _hitColor = new Color(1.0f, 1.0f, 1.0f, 1.0f); // White tint
	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f; // 50% white tint

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		// Add to trees group for duplicate detection
		AddToGroup("trees");

		// Get sprite component
		_sprite = GetNode<Sprite2D>("Sprite2D");
		if (_sprite == null)
		{
			GD.PrintErr("Tree: Sprite2D node not found!");
			return;
		}

		// Get HP bar component
		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("Tree: ProgressBar node not found!");
		}

		// Set up Y-sorting
		YSortEnabled = true;
		// Position sprite relative to tree center (16x32 sprite, so offset Y by 8 to bottom-align)
		_sprite.Position = new Vector2(0, 8);

		// For proper Y-sorting, the tree node's position should represent the bottom of the tree
		// Since you set transform.position.y to -16, the visual tree is moved up but Y-sorting uses node position
		// This should make the player render correctly when behind the tree

		// Find CustomDataLayerManager in scene
		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("Tree: CustomDataLayerManager not found!");
		}

		// Only calculate tile position if it hasn't been set by spawner
		if (_tilePosition == Vector2I.Zero)
		{
			// Calculate tile position from world position (for manually placed trees)
			_tilePosition = new Vector2I((int)(GlobalPosition.X - 8) / 16, (int)(GlobalPosition.Y - 8) / 16);

			// Mark tile as occupied (only for manually placed trees)
			_customDataManager?.SetObjectPlaced(_tilePosition, ObjectTypePlaced.Tree);
		}

		// Initialize HP bar
		UpdateHPBar();

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	public override void _Process(double delta)
	{
		// Handle transparency when player is behind tree
		UpdateTransparency();
	}

	/// <summary>
	/// Update transparency based on player position
	/// </summary>
	private void UpdateTransparency()
	{
		if (_sprite == null) return;

		// Find player in scene
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player == null) return;

		// Check if player is behind tree (player Y < tree Y in screen coordinates), + 8 to adjust height as tree is tall
		bool playerBehind = player.GlobalPosition.Y < GlobalPosition.Y + 8;

		// Check horizontal distance - only make transparent if player is reasonably close horizontally
		float horizontalDistance = Math.Abs(player.GlobalPosition.X - GlobalPosition.X);
		bool closeEnoughHorizontally = horizontalDistance <= 16.0f; // About 1 tiles

		// Check vertical distance - don't make transparent if player is too far above the tree
		float verticalDistance = GlobalPosition.Y - player.GlobalPosition.Y; // How far above player is
		bool notTooFarAbove = verticalDistance <= 16.0f; // About 2 tiles above tree

		// Set transparency only if player is behind, close horizontally, and not too far above
		var modulate = _sprite.Modulate;
		modulate.A = (playerBehind && closeEnoughHorizontally && notTooFarAbove) ? 0.5f : 1.0f;
		_sprite.Modulate = modulate;
	}

	/// <summary>
	/// Take damage from pickaxe
	/// </summary>
	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;

		// Update HP bar
		UpdateHPBar();

		// Play hit animation
		PlayHitAnimation();

		// Check if tree should be destroyed
		if (_currentHealth <= 0)
		{
			DestroyTree();
		}
	}

	/// <summary>
	/// Play hit animation (white tint + scale effect)
	/// </summary>
	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		// Stop any existing tween
		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		// White tint effect
		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		// Scale effect (smaller then bigger)
		var originalScale = _sprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_sprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	/// <summary>
	/// Destroy the tree and drop resources
	/// </summary>
	private void DestroyTree()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		// Drop resources
		DropResources();

		// Clear tile occupation
		_customDataManager?.ClearObjectPlaced(_tilePosition);

		// Give XP reward
		CommonSignals.Instance?.EmitAddXp(5);

		// Emit destruction signal
		EmitSignal(SignalName.TreeDestroyed, _tilePosition);

		// Remove from scene
		QueueFree();
	}

	/// <summary>
	/// Drop resources when tree is destroyed
	/// </summary>
	private void DropResources()
	{
		// Spawn multiple individual resources (each with quantity 1)
		for (int i = 0; i < ResourceAmount; i++)
		{
			// Add small random offset to prevent resources from stacking exactly
			Vector2 offset = new Vector2(
				(float)(GD.Randf() - 0.5f) * 8.0f, // Random X offset: -4 to +4 pixels
				(float)(GD.Randf() - 0.5f) * 8.0f  // Random Y offset: -4 to +4 pixels
			);
			Vector2 spawnPosition = GlobalPosition + offset;

			DroppedResource.SpawnResource(spawnPosition, ResourceType, 1);
		}
	}

	/// <summary>
	/// Get current health
	/// </summary>
	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	/// <summary>
	/// Set current health
	/// </summary>
	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	/// <summary>
	/// Get tile position
	/// </summary>
	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	/// <summary>
	/// Set tile position (used by spawner)
	/// </summary>
	public void SetTilePosition(Vector2I position)
	{
		_tilePosition = position;
		// Position object in the center of the tile (tile size is 16x16)
		// Visual offset: -16 on Y to make tree appear "on the ground"
		GlobalPosition = new Vector2(position.X * 16 + 8, position.Y * 16 + 8 - 16);


	}

	/// <summary>
	/// Check if this tree can be hit by pickaxe from given position
	/// </summary>
	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		// Tree can be hit if player is adjacent (including diagonally)
		var distance = _tilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	/// <summary>
	/// Update HP bar based on current health
	/// </summary>
	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		// Calculate health percentage
		float healthPercentage = (float)_currentHealth / MaxHealth;

		// If at full health, hide HP bar
		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			// Show HP bar and set progress
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (_tilePosition == tilePosition)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			if (CanBeHitFrom(playerTile))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}
}
