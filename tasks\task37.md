We are designing puzzle that player needs to resolve. Implement it with following tasks. Read very carefully all required files so that you don't make any mistakes and I don't need to fix anything. All needs to work as i described below. Create tasks for yourself, implement and make sure it works:

1. res://scenes/regions/region6/GraveyardCandle.tscn - here i added candle, it has PlayerDetector. Look at player.tscn where player has PlayerDetector - check on which layer it uses to detect player. Then in GraveyardCandle.tscn - set appropriate layer to PlayerDetector (in tscn!).

2. When player is in range - he can interact. In player.tscn I added Key node - read player.tscn to see where it is added and what it contains. So when player is in range of graveyard candle - we should play animation player PressKey animation from Keys->KeyE->AnimationPlayer (it will make KeyE enabled). When player leaves, make KeyE node disabled.

3. When player clicks Interact action and player is in this range of graveyard candle - it should emit signal from common signals. Connect it to script in Region6QuestSystem scene - which contains 8 candles. Each candle needs Id (int). In game save we need to store which candles are lit. Candles needs to be processed in Region6QuestSystem.cs. When player restarts game - lit candles needs to be restored (play animation). Read Region6QuestSystem.tscn(!!!) to see how it works. GraveyardCandle has AnimationPlayer - it needs to play On andimation when candle is unlocked (it is loop animation). Initially play Off animation - it will setup all visibilities etc to off.

4. We want to add a special animation sequence when player unlocks all graveyard candles:
* initially in Region6QuestSystem, we want to make invisible following items:
- in Animators node there are Candle1Effect-Candle8Effect and inside each there is AnimationPlayer. Make Candle1Effect-Candle8Effect invisible initially. When all candles are lit - make them visible and then play animation on Candle1Effect-Candle8Effect - (eg Candle1Effect->AnimationPlayer->Play("On")) - this animation plays an effect in the loop.
- in Region6QuestSystem.tscn there is also AnimationPlayer. When all candles are unlocked - after making candle effects visible and starting their animaitons - then you need to play Region6QuestSystem->AnimationPlayer->Play("Unlock"). This animation will play some effects and then when they ends - you need to play Region6QuestSystem->SculptureRegion6->AnimationPlayer->Play("On") - this will make sculpture play some cool looped animation.

* when player restarts game and all candles are lit then you need to:
- in each GraveyardCandle1 play On animation
- in SculptureRegion6 play On animation
- you can queue free node Animators as they will not be used again.


5. Read campfire.tscn and it's script. It has lighting source. We need to add something similar to GraveyardCandle.tscn. And also, we need to add something similar to Region6QuestSystem->Animators->to all Candle[x]Effect. This light source should only play if its night - just like in campfire

6. Make sure if project builds.