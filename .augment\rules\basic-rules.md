---
type: "always_apply"
---

<guidance>
* Make sure you don't change (break) any enum numerations.
* Don't add comment.

* It is very important that you understand deeply our system so that you don't make mistakes - read all required files (tscn and cs - both) so that you understand how it all works. Implement code only when you fully understand it (both cs and tscn) - even if you would need to read many files. You need to be 100% sure that you understand it before you implement it so that it works at first try.

*Build project when you end to make sure it compiles.
</guidance>