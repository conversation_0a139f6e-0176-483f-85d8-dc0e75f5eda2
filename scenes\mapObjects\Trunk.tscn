[gd_scene load_steps=7 format=3 uid="uid://b3jaebi6nxrdy"]

[ext_resource type="Script" uid="uid://bqk8wa5i6c5o8" path="res://scenes/mapObjects/Trunk.cs" id="1_trunk"]
[ext_resource type="AudioStream" uid="uid://46yex8wh4k8c" path="res://resources/audio/ovani/Chisel C.ogg" id="2_05ibf"]
[ext_resource type="AudioStream" uid="uid://c7wmjx041h8iv" path="res://resources/audio/ovani/Rock Dirt Impact Dull D.ogg" id="2_5py5p"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]
[ext_resource type="PackedScene" uid="uid://brwmy8gpv2x3p" path="res://scenes/Audio/EffectPlayer.tscn" id="4_48ybp"]
[ext_resource type="Texture2D" uid="uid://dh5brsh6dnjch" path="res://resources/solaria/exterior/treeTrunk.png" id="4_b5fm6"]

[node name="Trunk" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_trunk")
RockDestroyedAudio = ExtResource("2_5py5p")
RockHit = ExtResource("2_05ibf")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("4_b5fm6")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(7, 4, 2, 7, -2, 7, -7, 4, -7, 1, -5, 0, -5, -2, 5, -2, 5, 0, 7, 1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)

[node name="EffectPlayer" parent="." instance=ExtResource("4_48ybp")]
