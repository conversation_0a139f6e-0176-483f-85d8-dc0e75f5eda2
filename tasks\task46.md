TASK-1: In Region9Statue - there is a PlayerDetector area2d (read tscn!). When player is in this region then i want to send signal from common signals - ShowKeyEPrompt (this area detection should be same as in player tscn in PlayerDetector - verify this detector on which layer it detects etc - read tscn), when player exits this area then send signal Hi<PERSON><PERSON><PERSON>EPrompt. Then when player clicks E key and is in this area, i want to play animation Open (from AnimationPlayer) that will open menu (it will handle visibility etc) and when player clicks CloseButton (read this tscn to see where it is) then play Close animation - it will handle visibility etc. When player opens panel for the first time, you need to make from GameData, statue buff with Id 'StatueBuff1' IsUnlocked to true.

TASK-2: In Region9Statue - There is <PERSON><PERSON><PERSON><PERSON><PERSON> with inside has Header and Description (read tscn to see where it is). I want you to change it's text and translate it, so <PERSON><PERSON> needs to be 'Statue' and Description needs to tell (in short) that this staue buff is now unlocked and player can select buff from this statue in his home (tell this in short as i dont have much space there). So add some key placeholders for this (translation) and add translation in translations csv - similar way to other translations.

TASK-3: In region manager of region 5, i want you to spawn <PERSON><PERSON> in similar way that other items are spawned but in a 