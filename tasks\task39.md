<guidance>
Implement following tasks. Make sure you don't change any enum numerations.
It is very important that you understand deeply our system so that you don't make mistakes - read all required files (tscn and cs - both) so that you understand how it all works.
Don't create comment.
Build project when you end.
</guidance>

TASK-1:
Duplicate Tree.tscn and Tree.cs -> I want exact 1:1 copy, with same begaviour, same position setting etc. I want to duplicate them to create new tree: BigGreenBush. It will  drop 3 leaves and 1 wood and 1 brantch. It has to have a bit less hp than three.

TASK-2:
In region1-4 managers add a chance to spawn BigGreenBush (5% chance for each region and subtract 2% chance from tree spawn chance and 3% from bush - if bush not exists then remove 1% from 3 items that have biggest probability).

TASK-3:
Duplicate Rock.tscn scene and Rock.cs -> I want exact 1:1 copy, with same begaviour, same position setting etc. I want to duplicate them to create Trunk. Trunk needs to have 5% chance to spawn in Region5 manager. Subtract 5% from some other resources in that region manager (starting from most possible to spawn).

TASK-4:
Duplicate region 5 manager and call it region 6 manager. Duplicate 1:1. When region 6 is unlocked - it should spawn 4 random items. it coud spawn in total max 24 items. We don't spawn enemies in region 6 - it should be a simple region. Region6Manager should initialize data just like for example region2manager - when it's unlocked.

TASK-5:
Duplicate chest and create Chest2. It should be 1:1 copy but it needs to require 1x CopperKey. I will set this chest texture.

TASK-6:
In region manager 6 you need to spawn a Chest2 when all graveyard candles are lit. The chest should spawn at position (6,-13 -> it's tile number not global coordination, so spawn in the center of this tile) in region 6. The chest should contain 100 gold and 20 copper ore. There should be also 2 Chests (standard chest) that needs wooden key, that will spawn at 5,-13 and 7,-13 (in center of that tiles). So they should contain: 10 gold and 5 copper bars, and second 25 gold and 5 CarrotSeedBag.

Chests needs to be saved/loaded like other chests (unless they are already opened).

TASK-7:
In region manager 1-6, in TrySpawnObject() - add an optional parameter ObjectType. If it's passed - then don't roll for random object type, but use this one. Then in region1manager, in place, where you spawn initial resources - spawn 6 trees, 6 rocks, 3 berry bushes (i mean pass it in TrySpawnObject())