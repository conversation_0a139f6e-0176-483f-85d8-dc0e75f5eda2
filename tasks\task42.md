Read in detail enemy_system.md and understand what we are planning. Analize how <PERSON><PERSON><PERSON><PERSON><PERSON> works and all additional scenes and scripts connected to it. We are preparing advanced combat logic for AAA quality (highest quality, big budget) game.
I want you to be very precision and quality oriented. I want to have very good quality game with most advanced combat AI system as possible - be creative and make the system to look as good as possible, that looks as natural as possible.

Overview: into our fighting/enemy system - I want to add a new feature: in region 8 I want to add a spawn for enemies that will spawn enemies - initially goblins only (but later I want to chang this to spawn other types of enemies too). As for now, I have created a scene - EnemySpawnRegion8.tscn that is added to world->RegionSpecific->Region8. You need to read it. Here is some description of it's nodes that you should know about: 
* Fireplace1 and Fireplace2 - they have child node AnimationPlayer - you need to play animation 'Animate' on both of them. 
* SpawnMonument and SpawnMonument2 - they have child node AnimationPlayer - you need to play animation 'Animate' on both of them. That nodes can be destroyed (it's the spawner of enemies) and then SpawnMonumentDestroyed should appear and those 2 nodes should be hidden. Also, when destroyed, Colliders->CollisionPolygon2D3, Colliders->CollisionPolygon2D4 and Colliders->CollisionPolygon2D5 should be disabled and Colliders->CollisionPolygon2D6 should be enabled.
*  SpawnMonumentDestroyed - it's a node that should appear when spawner is destroyed. It's already there but it's hidden. When spawner is destroyed you need to show it. When monument is restored - it should be hidden again.
* After player destroys spawner - it should not spawne enemies but should restore after 8 minutes. When it's restored - restore SpawnMonument, SpawnMonument2 and hide SpawnMonumentDestroyed. Also, enable Colliders->CollisionPolygon2D3, Colliders->CollisionPolygon2D4 and Colliders->CollisionPolygon2D5. And disable Colliders->CollisionPolygon2D6.
* SpawnMonumentArea - area where new enemies can be spawned.
* We have SpawnMonumentArea - you can use it to detect when player hits given monument with sword or bow arrow. It should take damage when player hits it with sword or bow arrow. You can look how player attack MeleeGoblin - but here, we need to detect when player hits area defined by SpawnMonumentArea (sword - similar logic when player hit melee goblin, bow - similar logic when player hit bow goblin but both adjusted to this area).
* When monument has less than full hp - show health bar above it. You can use MonumentHealth node for that. It's already there but it's hidden. Show it when monument has less than full hp. You can see how to use it - MeleeGoblin has similar health bar but it's called ProgressBar there.
* Monument should spawn 1 goblin per 2 minutes. There should be max 6 goblins spawned at a time. Initially goblins needs to be 'teritorial' - they should not attack player but only when he enters their range/follow in given area. When there are 6 goblins spawned - we need to make 4 of them 'aggressive' - they should attack player and buildings.
* You need to plan best infrastructure for this all. Think about extensibility - we might add other enemy types later and other regions that will also have some spawns that will spawn enemies in similar way that they can pursue player/building. We can use for example 1) this new scene that i created and 2) Region8Manager - not yet existing - but it should be a 1:1 copy of Region7Manager with additional required logic. You can also create other required scripts etc.
* Don't break current system - for MeleeGoblin that is teritorial, we use it in region 5 (we have also world->RegionSpecific->Region5->GoblinTerritory1).
* Make sure project builds.
* Update enemies_system.md with all the details of this new system.
