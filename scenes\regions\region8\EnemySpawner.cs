using Godot;
using System;
using System.Collections.Generic;

public partial class EnemySpawner : Node2D
{
	[Export] public int MaxHealth { get; set; } = 50;
	[Export] public float SpawnInterval { get; set; } = 120.0f; // 2 minutes
	[Export] public float RestoreTime { get; set; } = 480.0f; // 8 minutes
	[Export] public int MaxEnemies { get; set; } = 6;

	// Node references
	private Sprite2D _spawnMonument;
	private Sprite2D _spawnMonument2;
	private Sprite2D _spawnMonumentDestroyed;
	private Sprite2D _fireplace1;
	private Sprite2D _fireplace2;
	private Area2D _spawnMonumentArea;
	private Area2D _enemySpawnArea;
	private ProgressBar _monumentHealth;
	private StaticBody2D _colliders;
	private AnimationPlayer _fireplace1Animation;
	private AnimationPlayer _fireplace2Animation;
	private AnimationPlayer _monument1Animation;
	private AnimationPlayer _monument2Animation;

	// Collision shapes for monument states
	private CollisionPolygon2D _collider3; // Active monument colliders
	private CollisionPolygon2D _collider4;
	private CollisionPolygon2D _collider5;
	private CollisionPolygon2D _collider6; // Destroyed monument collider

	// Internal state
	private int _currentHealth;
	private bool _isDestroyed = false;
	private Timer _spawnTimer;
	private Timer _restoreTimer;
	private Region8Manager _regionManager;
	private Random _random = new();

	public override void _Ready()
	{
		_currentHealth = MaxHealth;
		
		// Get node references
		_spawnMonument = GetNode<Sprite2D>("SpawnMonument");
		_spawnMonument2 = GetNode<Sprite2D>("SpawnMonument2");
		_spawnMonumentDestroyed = GetNode<Sprite2D>("SpawnMonumentDestroyed");
		_fireplace1 = GetNode<Sprite2D>("Fireplace1");
		_fireplace2 = GetNode<Sprite2D>("Fireplace2");
		_spawnMonumentArea = GetNode<Area2D>("SpawnMonumentArea");
		_enemySpawnArea = GetNode<Area2D>("EnemySpawnArea");
		_monumentHealth = GetNode<ProgressBar>("MonumentHealth");
		_colliders = GetNode<StaticBody2D>("Colliders");

		// Get animation players
		_fireplace1Animation = _fireplace1.GetNode<AnimationPlayer>("AnimationPlayer");
		_fireplace2Animation = _fireplace2.GetNode<AnimationPlayer>("AnimationPlayer");
		_monument1Animation = _spawnMonument.GetNode<AnimationPlayer>("AnimationPlayer");
		_monument2Animation = _spawnMonument2.GetNode<AnimationPlayer>("AnimationPlayer");

		// Get collision shapes
		_collider3 = _colliders.GetNode<CollisionPolygon2D>("CollisionPolygon2D3");
		_collider4 = _colliders.GetNode<CollisionPolygon2D>("CollisionPolygon2D4");
		_collider5 = _colliders.GetNode<CollisionPolygon2D>("CollisionPolygon2D5");
		_collider6 = _colliders.GetNode<CollisionPolygon2D>("CollisionPolygon2D6");

		// Start animations
		StartAnimations();

		// Connect to combat signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		// Connect area signals for arrow detection
		_spawnMonumentArea.AreaEntered += OnArrowEntered;

		// Setup timers
		_spawnTimer = new Timer();
		_spawnTimer.WaitTime = SpawnInterval;
		_spawnTimer.OneShot = false;
		_spawnTimer.Timeout += OnSpawnTimer;
		AddChild(_spawnTimer);

		_restoreTimer = new Timer();
		_restoreTimer.WaitTime = RestoreTime;
		_restoreTimer.OneShot = true;
		_restoreTimer.Timeout += OnRestoreTimer;
		AddChild(_restoreTimer);

		// Start spawning if not destroyed
		if (!_isDestroyed)
		{
			_spawnTimer.Start();
		}

		// Load saved state
		LoadSpawnerState();
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}
	}

	public void SetRegionManager(Region8Manager regionManager)
	{
		_regionManager = regionManager;
	}

	public EnemySpawner GetEnemySpawner()
	{
		return this;
	}

	private void StartAnimations()
	{
		// Start fireplace animations
		_fireplace1Animation?.Play("Animate");
		_fireplace2Animation?.Play("Animate");

		// Start monument animations if not destroyed
		if (!_isDestroyed)
		{
			_monument1Animation?.Play("Animate");
			_monument2Animation?.Play("Animate");
		}
	}

	private void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		if (_isDestroyed) return;

		// Check if monument area is within sword attack range
		if (IsInSwordAttackRange(playerPosition, attackDirection))
		{
			TakeDamage(2); // Sword damage
		}
	}

	private void OnArrowEntered(Area2D area)
	{
		if (_isDestroyed) return;

		// Check if it's an arrow
		if (area.GetParent() is Arrow arrow)
		{
			TakeDamage(3); // Arrow damage
			arrow.QueueFree();
		}
	}

	private bool IsInSwordAttackRange(Vector2 playerPosition, Vector2 attackDirection)
	{
		// Check if monument is within sword attack arc (similar to enemy detection)
		float distanceToMonument = playerPosition.DistanceTo(GlobalPosition);
		if (distanceToMonument > 32.0f) return false; // Attack range

		Vector2 directionToMonument = (GlobalPosition - playerPosition).Normalized();
		float angle = attackDirection.AngleTo(directionToMonument);
		float arcAngle = Mathf.DegToRad(180.0f); // 180-degree arc

		return Mathf.Abs(angle) <= arcAngle / 2.0f;
	}

	private void TakeDamage(int damage)
	{
		if (_isDestroyed) return;

		_currentHealth -= damage;
		UpdateHealthBar();

		if (_currentHealth <= 0)
		{
			DestroyMonument();
		}
		else
		{
			ShowHealthBar();
		}

		GD.Print($"EnemySpawner: Took {damage} damage, health: {_currentHealth}/{MaxHealth}");
	}

	private void UpdateHealthBar()
	{
		if (_monumentHealth != null)
		{
			_monumentHealth.SetProgress((float)_currentHealth / MaxHealth * 100.0f);
		}
	}

	private void ShowHealthBar()
	{
		if (_monumentHealth != null && _currentHealth < MaxHealth)
		{
			_monumentHealth.Visible = true;
		}
	}

	private void HideHealthBar()
	{
		if (_monumentHealth != null)
		{
			_monumentHealth.Visible = false;
		}
	}

	private void DestroyMonument()
	{
		_isDestroyed = true;
		_currentHealth = 0;

		// Hide active monuments
		_spawnMonument.Visible = false;
		_spawnMonument2.Visible = false;

		// Show destroyed monument
		_spawnMonumentDestroyed.Visible = true;

		// Update colliders
		_collider3.Disabled = true;
		_collider4.Disabled = true;
		_collider5.Disabled = true;
		_collider6.Disabled = false;

		// Stop spawning
		_spawnTimer.Stop();

		// Hide health bar
		HideHealthBar();

		// Start restore timer
		_restoreTimer.Start();

		// Save state
		SaveSpawnerState();

		GD.Print("EnemySpawner: Monument destroyed! Will restore in 8 minutes.");
	}

	private void RestoreMonument()
	{
		_isDestroyed = false;
		_currentHealth = MaxHealth;

		// Show active monuments
		_spawnMonument.Visible = true;
		_spawnMonument2.Visible = true;

		// Hide destroyed monument
		_spawnMonumentDestroyed.Visible = false;

		// Update colliders
		_collider3.Disabled = false;
		_collider4.Disabled = false;
		_collider5.Disabled = false;
		_collider6.Disabled = true;

		// Restart animations
		_monument1Animation?.Play("Animate");
		_monument2Animation?.Play("Animate");

		// Hide health bar (full health)
		HideHealthBar();

		// Restart spawning
		_spawnTimer.Start();

		// Save state
		SaveSpawnerState();

		GD.Print("EnemySpawner: Monument restored! Resuming enemy spawning.");
	}

	private void OnSpawnTimer()
	{
		if (_isDestroyed || _regionManager == null) return;

		// Check if we can spawn more enemies
		int currentEnemyCount = _regionManager.GetCurrentEnemyCount();
		if (currentEnemyCount >= MaxEnemies)
		{
			GD.Print($"EnemySpawner: Enemy limit reached ({currentEnemyCount}/{MaxEnemies})");
			return;
		}

		// Get random spawn position within spawn area
		Vector2 spawnPosition = GetRandomSpawnPosition();
		if (spawnPosition != Vector2.Zero)
		{
			_regionManager.SpawnEnemyFromMonument(spawnPosition);
			GD.Print($"EnemySpawner: Spawned enemy at {spawnPosition}");
		}
	}

	private void OnRestoreTimer()
	{
		RestoreMonument();
	}

	private Vector2 GetRandomSpawnPosition()
	{
		// Get spawn area bounds
		var shape = _enemySpawnArea.GetNode<CollisionShape2D>("CollisionShape2D").Shape as RectangleShape2D;
		if (shape == null) return Vector2.Zero;

		Vector2 areaPosition = _enemySpawnArea.GlobalPosition;
		Vector2 areaSize = shape.Size;

		// Generate random position within area
		float randomX = _random.NextSingle() * areaSize.X - areaSize.X / 2;
		float randomY = _random.NextSingle() * areaSize.Y - areaSize.Y / 2;

		return areaPosition + new Vector2(randomX, randomY);
	}

	private void SaveSpawnerState()
	{
		// Save spawner state to game data
		string key = "enemy_spawner_region_8";
		var spawnerData = new Dictionary<string, Variant>
		{
			["current_health"] = _currentHealth,
			["is_destroyed"] = _isDestroyed,
			["restore_time_remaining"] = _isDestroyed ? _restoreTimer.TimeLeft : 0.0
		};

		GameSaveData.Instance.WorldData.CustomLayerData[key] = spawnerData;
		GD.Print($"EnemySpawner: Saved state - Health: {_currentHealth}, Destroyed: {_isDestroyed}");
	}

	private void LoadSpawnerState()
	{
		// Load spawner state from game data
		string key = "enemy_spawner_region_8";
		if (GameSaveData.Instance.WorldData.CustomLayerData.ContainsKey(key))
		{
			var spawnerData = GameSaveData.Instance.WorldData.CustomLayerData[key];
			if (spawnerData is Dictionary<string, Variant> data)
			{
				_currentHealth = data.GetValueOrDefault("current_health", MaxHealth).AsInt32();
				_isDestroyed = data.GetValueOrDefault("is_destroyed", false).AsBool();
				double restoreTimeRemaining = data.GetValueOrDefault("restore_time_remaining", 0.0).AsDouble();

				if (_isDestroyed)
				{
					// Apply destroyed state without triggering destroy logic
					_spawnMonument.Visible = false;
					_spawnMonument2.Visible = false;
					_spawnMonumentDestroyed.Visible = true;
					_collider3.Disabled = true;
					_collider4.Disabled = true;
					_collider5.Disabled = true;
					_collider6.Disabled = false;
					_spawnTimer.Stop();
					HideHealthBar();

					// Continue restore timer if needed
					if (restoreTimeRemaining > 0)
					{
						_restoreTimer.WaitTime = restoreTimeRemaining;
						_restoreTimer.Start();
					}
					else
					{
						// Should restore immediately
						CallDeferred(nameof(RestoreMonument));
					}
				}
				else
				{
					UpdateHealthBar();
					if (_currentHealth < MaxHealth)
					{
						ShowHealthBar();
					}
				}

				GD.Print($"EnemySpawner: Loaded state - Health: {_currentHealth}, Destroyed: {_isDestroyed}");
			}
		}
	}
}
