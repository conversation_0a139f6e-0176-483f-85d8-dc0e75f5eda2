[gd_scene load_steps=7 format=3 uid="uid://cykmmk31j85a6"]

[ext_resource type="Script" uid="uid://dr6u4hok4p2dh" path="res://scenes/mapObjects/Barrel.cs" id="1_barrel"]
[ext_resource type="AudioStream" uid="uid://46yex8wh4k8c" path="res://resources/audio/ovani/Chisel C.ogg" id="2_05ibf"]
[ext_resource type="AudioStream" uid="uid://c7wmjx041h8iv" path="res://resources/audio/ovani/Rock Dirt Impact Dull D.ogg" id="2_5py5p"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]
[ext_resource type="PackedScene" uid="uid://brwmy8gpv2x3p" path="res://scenes/Audio/EffectPlayer.tscn" id="4_48ybp"]
[ext_resource type="Texture2D" uid="uid://cn6d6hkkrxfwc" path="res://resources/solaria/exterior/barrel.png" id="4_dw60y"]

[node name="Barrel" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_barrel")
BarrelDestroyedAudio = ExtResource("2_5py5p")
BarrelHit = ExtResource("2_05ibf")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("4_dw60y")
hframes = 3

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(7, 3, 6, 5, 2, 8, -2, 8, -6, 5, -7, 3, -7, 0, 7, 0)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)

[node name="EffectPlayer" parent="." instance=ExtResource("4_48ybp")]
