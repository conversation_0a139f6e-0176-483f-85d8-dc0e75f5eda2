[gd_scene load_steps=5 format=3 uid="uid://cbgclp7xhqtsd"]

[ext_resource type="Texture2D" uid="uid://dprgquega8bga" path="res://resources/solaria/exterior/houses/house2.png" id="1_ev1fh"]
[ext_resource type="PackedScene" uid="uid://bmvnt1kexku0y" path="res://scenes/Portal.tscn" id="2_va46p"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_ev1fh"]
size = Vector2(48, 59)

[sub_resource type="CircleShape2D" id="CircleShape2D_va46p"]

[node name="HouseExterior" type="Node2D"]
y_sort_enabled = true

[node name="House" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, 24)
texture = ExtResource("1_ev1fh")
offset = Vector2(0, -24)

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 8.5)
shape = SubResource("RectangleShape2D_ev1fh")

[node name="HomeEntrance" type="Area2D" parent="." groups=["navigation_polygon_source_geometry_group"]]

[node name="CollisionShape2D" type="CollisionShape2D" parent="HomeEntrance"]
position = Vector2(0, 35)
scale = Vector2(0.935, 0.275)
shape = SubResource("CircleShape2D_va46p")

[node name="Portal" parent="." instance=ExtResource("2_va46p")]
position = Vector2(0, 25)
