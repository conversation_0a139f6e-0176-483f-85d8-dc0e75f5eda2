We want to add a "teleport" that will change scene when player enters it.

So:
1. I created Portal.tscn. READ IT. It has PlayerDetector when player enters it (check Player.tscn to see which layer is used by PlayerDetector in Player.tscn and set the same in Portal.tscn).
2. When player enters it:
* show <PERSON><PERSON><PERSON><PERSON><PERSON> (make it visible, it's invisible by default)
* change current root scene to given scene
* in order to determine what scene needs to be changed to, we need to add exports: SceneFrom (enum), SceneTo (enum) - create enum for scenes. Currently we have 2 scenes like this: World and HouseInterior.
* we need hardcoded locations of scenes - so we know which scene to switch to
* use GetTree().ChangeSceneToFile()
* before switching - we need to set player position - GameSaveData.Instance.PlayerStats.Position -> this position will also needs to be hardcoded for each scene from and scene to (so if we switch from HomeInterior to World then we need to set player position to be in the world at specific location - i will set it hardcoded for all possible transitions)

So for now player can move between world and house interior.