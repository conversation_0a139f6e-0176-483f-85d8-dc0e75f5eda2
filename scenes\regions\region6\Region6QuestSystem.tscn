[gd_scene load_steps=16 format=3 uid="uid://c2e258d7v80qf"]

[ext_resource type="PackedScene" uid="uid://ocpnbejvu51t" path="res://scenes/regions/region6/GraveyardCandle.tscn" id="1_740o1"]
[ext_resource type="Script" uid="uid://buvi6brb0rdr0" path="res://scenes/regions/region6/Region6QuestSystem.cs" id="1_script"]
[ext_resource type="Script" uid="uid://bacg58aeumyfa" path="res://scenes/regions/region6/EffectLight.cs" id="2_effectlight"]
[ext_resource type="PackedScene" uid="uid://csf2nu8cqdjp1" path="res://scenes/regions/region6/SculptureRegion6.tscn" id="2_h1g57"]
[ext_resource type="Texture2D" uid="uid://dwfcgxxcc6mjr" path="res://resources/ELV_itchio/Light Spells/Lightning_Ball_Y.png" id="3_fptkh"]
[ext_resource type="Texture2D" uid="uid://b6yoddmprsxe" path="res://resources/ELV_itchio/Light Spells/Lightning_Explosion_Y.png" id="4_4403a"]
[ext_resource type="Texture2D" uid="uid://bbl5rxitguek5" path="res://resources/ELV_itchio/Light Spells/Lightning_Cast_Y.png" id="5_k855p"]

[sub_resource type="Animation" id="Animation_snh0o"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Animators/Candle1Effect:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(8, 12)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Animators/Candle2Effect:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(96, 18)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Animators/Candle3Effect:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(184, 20)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Animators/Candle1Effect:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Animators/Candle2Effect:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Animators/Candle3Effect:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Animators/Candle4Effect:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(179, 99)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Animators/Candle4Effect:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Animators/Candle5Effect:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(182, 187)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Animators/Candle5Effect:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Animators/Candle6Effect:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(96, 187)]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Animators/Candle6Effect:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Animators/Candle7Effect:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(8.99999, 185)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Animators/Candle7Effect:visible")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Animators/Candle8Effect:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(11, 99)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Animators/Candle8Effect:visible")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Animators/Explosion:frame")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Animators/Explosion2:frame")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Animators/Explosion:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Animators/Explosion2:visible")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_fptkh"]
resource_name = "Unlock"
length = 3.6
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Animators/Candle1Effect:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(8, 12), Vector2(89, 71)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Animators/Candle2Effect:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(96, 18), Vector2(97, 70)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Animators/Candle3Effect:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(184, 20), Vector2(103, 72)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Animators/Candle1Effect:visible")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Animators/Candle2Effect:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Animators/Candle3Effect:visible")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Animators/Candle4Effect:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(179, 99), Vector2(103, 78)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Animators/Candle4Effect:visible")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Animators/Candle5Effect:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(182, 187), Vector2(100, 81)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Animators/Candle5Effect:visible")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Animators/Candle6Effect:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(96, 187), Vector2(96, 83)]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Animators/Candle6Effect:visible")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Animators/Candle7Effect:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(7.99999, 187), Vector2(91, 82)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Animators/Candle7Effect:visible")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Animators/Candle8Effect:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(11, 99), Vector2(89, 78)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Animators/Candle8Effect:visible")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Animators/Explosion:frame")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3, 3.1, 3.2, 3.3, 3.4, 3.5),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Animators/Explosion2:frame")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3, 3.1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Animators/Explosion:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(2.1),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Animators/Explosion2:visible")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(2.1),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_8nh03"]
_data = {
&"RESET": SubResource("Animation_snh0o"),
&"Unlock": SubResource("Animation_fptkh")
}

[sub_resource type="Animation" id="Animation_4403a"]
resource_name = "Play"
length = 0.8
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7]
}

[sub_resource type="Animation" id="Animation_k855p"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_snh0o"]
_data = {
&"Play": SubResource("Animation_4403a"),
&"RESET": SubResource("Animation_k855p")
}

[sub_resource type="Gradient" id="Gradient_effectlight"]
offsets = PackedFloat32Array(0, 0.769634)
colors = PackedColorArray(1, 1, 1, 1, 1, 1, 1, 0)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_effect"]
gradient = SubResource("Gradient_effectlight")
width = 32
height = 32
fill = 1
fill_from = Vector2(0.5, 0.5)

[node name="Region6QuestSystem" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_script")

[node name="GraveyardCandle1" parent="." instance=ExtResource("1_740o1")]
position = Vector2(0, 9)

[node name="GraveyardCandle2" parent="." instance=ExtResource("1_740o1")]
position = Vector2(96, 9)
CandleId = 2

[node name="GraveyardCandle3" parent="." instance=ExtResource("1_740o1")]
position = Vector2(192, 9)
CandleId = 3

[node name="GraveyardCandle4" parent="." instance=ExtResource("1_740o1")]
position = Vector2(0, 105)
CandleId = 4

[node name="GraveyardCandle5" parent="." instance=ExtResource("1_740o1")]
position = Vector2(192, 105)
CandleId = 5

[node name="GraveyardCandle6" parent="." instance=ExtResource("1_740o1")]
position = Vector2(0, 201)
CandleId = 6

[node name="GraveyardCandle7" parent="." instance=ExtResource("1_740o1")]
position = Vector2(96, 201)
CandleId = 7

[node name="GraveyardCandle8" parent="." instance=ExtResource("1_740o1")]
position = Vector2(192, 201)
CandleId = 8

[node name="SculptureRegion6" parent="." instance=ExtResource("2_h1g57")]
position = Vector2(96, 73.84)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_8nh03")
}

[node name="Animators" type="Node2D" parent="."]

[node name="Candle1Effect" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(8, 12)
rotation = 0.715585
texture = ExtResource("3_fptkh")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="Animators/Candle1Effect"]
libraries = {
&"": SubResource("AnimationLibrary_snh0o")
}

[node name="EffectLight" type="PointLight2D" parent="Animators/Candle1Effect"]
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Candle1Effect"]
script = ExtResource("2_effectlight")

[node name="Candle2Effect" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(96, 18)
rotation = 1.5708
texture = ExtResource("3_fptkh")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="Animators/Candle2Effect"]
libraries = {
&"": SubResource("AnimationLibrary_snh0o")
}

[node name="EffectLight" type="PointLight2D" parent="Animators/Candle2Effect"]
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Candle2Effect"]
script = ExtResource("2_effectlight")

[node name="Candle3Effect" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(184, 20)
rotation = 2.2061
texture = ExtResource("3_fptkh")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="Animators/Candle3Effect"]
libraries = {
&"": SubResource("AnimationLibrary_snh0o")
}

[node name="EffectLight" type="PointLight2D" parent="Animators/Candle3Effect"]
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Candle3Effect"]
script = ExtResource("2_effectlight")

[node name="Candle4Effect" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(179, 99)
rotation = -2.80474
texture = ExtResource("3_fptkh")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="Animators/Candle4Effect"]
libraries = {
&"": SubResource("AnimationLibrary_snh0o")
}

[node name="EffectLight" type="PointLight2D" parent="Animators/Candle4Effect"]
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Candle4Effect"]
script = ExtResource("2_effectlight")

[node name="Candle5Effect" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(182, 187)
rotation = -2.32652
texture = ExtResource("3_fptkh")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="Animators/Candle5Effect"]
libraries = {
&"": SubResource("AnimationLibrary_snh0o")
}

[node name="EffectLight" type="PointLight2D" parent="Animators/Candle5Effect"]
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Candle5Effect"]
script = ExtResource("2_effectlight")

[node name="Candle6Effect" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(96, 187)
rotation = -1.5708
texture = ExtResource("3_fptkh")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="Animators/Candle6Effect"]
libraries = {
&"": SubResource("AnimationLibrary_snh0o")
}

[node name="EffectLight" type="PointLight2D" parent="Animators/Candle6Effect"]
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Candle6Effect"]
script = ExtResource("2_effectlight")

[node name="Candle7Effect" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(8.99999, 185)
rotation = -1.03149
texture = ExtResource("3_fptkh")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="Animators/Candle7Effect"]
libraries = {
&"": SubResource("AnimationLibrary_snh0o")
}

[node name="EffectLight" type="PointLight2D" parent="Animators/Candle7Effect"]
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Candle7Effect"]
script = ExtResource("2_effectlight")

[node name="Candle8Effect" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(11, 99)
rotation = -0.55676
texture = ExtResource("3_fptkh")
hframes = 6
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="Animators/Candle8Effect"]
libraries = {
&"": SubResource("AnimationLibrary_snh0o")
}

[node name="EffectLight" type="PointLight2D" parent="Animators/Candle8Effect"]
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Candle8Effect"]
script = ExtResource("2_effectlight")

[node name="Explosion" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(96, 75)
scale = Vector2(2.255, 2.255)
texture = ExtResource("4_4403a")
hframes = 6
vframes = 4

[node name="EffectLight" type="PointLight2D" parent="Animators/Explosion"]
z_index = 1
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Explosion"]
z_index = 1
script = ExtResource("2_effectlight")

[node name="Explosion2" type="Sprite2D" parent="Animators"]
z_index = 1
position = Vector2(91, 34)
texture = ExtResource("5_k855p")
hframes = 6
vframes = 4

[node name="EffectLight" type="PointLight2D" parent="Animators/Explosion2"]
z_index = 1
color = Color(0.8, 0.9, 1, 1)
texture = SubResource("GradientTexture2D_effect")
texture_scale = 2.0

[node name="EffectLightScript" type="Node2D" parent="Animators/Explosion2"]
z_index = 1
script = ExtResource("2_effectlight")
